<<<<<<< HEAD
# SpecterShift Game

A 3D game built with React, Three.js, and Node.js.

## Features

- 3D gameplay with WebGL rendering
- Multiplayer support via WebSockets
- Procedural level generation
- Pet Specters with AI behaviors
- Persistent storage with PostgreSQL

## Setup

### Prerequisites

- Node.js 18+
- PostgreSQL 14+

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/spectershift.git
cd spectershift
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Edit the `.env` file to configure your database connection:
```
DATABASE_URL=postgres://username:password@localhost:5432/spectershift
```

5. Create a PostgreSQL database:
```bash
createdb spectershift
```

6. Run database migrations:
```bash
npm run db:push
```

7. Start the development server:
```bash
npm run dev
```

## Database Setup

The game uses PostgreSQL for persistent storage. If you don't have a PostgreSQL database set up, the game will fall back to in-memory storage (data will be lost on server restart).

### Setting Up PostgreSQL

1. Install PostgreSQL:
   - Ubuntu/Debian: `sudo apt install postgresql`
   - macOS: `brew install postgresql`
   - Windows: Download from [postgresql.org](https://www.postgresql.org/download/windows/)

2. Create a database:
```bash
createdb spectershift
```

3. Update your `.env` file with the correct connection string:
```
DATABASE_URL=postgres://username:password@localhost:5432/spectershift
```

4. Run migrations:
```bash
npm run db:push
```

## Development

### Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build for production
- `npm run start` - Start the production server
- `npm run db:push` - Push database schema changes to the database

## Deployment

See `deployment_instructions.md` for detailed deployment instructions.
=======
# SHATTERSHIFT
>>>>>>> origin/main
