import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes.js";
import { setupVite, serveStatic, log } from "./vite.js";
import { testConnection } from "./db.js";
import { runMigrations } from "./migrate.js";
import { setupUnifiedWebSocketServer } from "./unifiedWebSocket.js";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Serve static files from the uploads directory
app.use('/uploads', express.static('uploads'));

// Add CORS headers for development
app.use((req, res, next) => {
  // CRITICAL FIX: Ensure CORS headers are properly set for WebSocket connections
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  // Add WebSocket-specific headers
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  try {
    // Test database connection and run migrations if needed
    console.log('Testing database connection...');
    const dbConnected = await testConnection();

    if (dbConnected) {
      console.log('Database connected, running migrations...');
      await runMigrations();
    } else {
      console.warn('Database connection failed, skipping migrations. Using in-memory storage.');
    }

    const server = await registerRoutes(app);

    // The unified WebSocket server is already made globally accessible in routes.ts

    // Add global error handler
    app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
      console.error("Server error:", err);
      const status = err.status || err.statusCode || 500;
      const message = err.message || "Internal Server Error";

      res.status(status).json({ message });
    });

    // importantly only setup vite in development and after
    // setting up all the other routes so the catch-all route
    // doesn't interfere with the other routes
    if (app.get("env") === "development") {
      await setupVite(app, server);
    } else {
      serveStatic(app);
    }

    // Use environment port with fallback to 5001
    const port = process.env.PORT || 5001;

    // CRITICAL FIX: For local development, bind to 127.0.0.1 instead of 0.0.0.0
    // This ensures that the server is accessible via 127.0.0.1 which is more reliable than localhost
    const host = process.env.NODE_ENV === 'production' ? '0.0.0.0' : '127.0.0.1';

    server.listen({
      port,
      host,
      reusePort: true,
    }, () => {
      log(`Server listening on ${host}:${port}`);
      console.log(`Unified WebSocket server is available at ws://${host}:${port}`);
      console.log(`Tournament battle connections available at ws://${host}:${port}/tournament`);
    });

    // Improved error handling for server
    server.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${port} is already in use. Please close the other application or use a different port.`);
        process.exit(1);
      } else {
        console.error('Server error:', error);
      }
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('Shutting down server gracefully...');
      server.close(() => {
        console.log('Server shut down successfully');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
})();