import {
  users,
  type User,
  type InsertUser,
  type HighScore,
  type InsertHighScore,
  type GameArena,
  type InsertGameArena,
  type GamePlayer,
  type InsertGamePlayer,
  type GameTeam,
  type InsertGameTeam,
  GameMode
} from "@shared/schema.js";

// Storage interface with CRUD methods for all entities
export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // High Score methods
  getHighScores(gameMode?: GameMode, limit?: number): Promise<HighScore[]>;
  saveHighScore(playerName: string, score: number, gameMode?: GameMode, teamName?: string): Promise<HighScore>;

  // Game Arena methods
  createGameArena(arena: InsertGameArena): Promise<GameArena>;
  getGameArena(id: number): Promise<GameArena | undefined>;
  getAllGameArenas(status?: string): Promise<GameArena[]>;
  updateGameArena(id: number, updates: Partial<GameArena>): Promise<GameArena | undefined>;
  deleteGameArena(id: number): Promise<boolean>;

  // Game Player methods
  addPlayerToArena(player: InsertGamePlayer): Promise<GamePlayer>;
  getPlayersInArena(arenaId: number): Promise<GamePlayer[]>;
  getPlayerById(id: number): Promise<GamePlayer | undefined>;
  getPlayerBySessionId(sessionId: string): Promise<GamePlayer | undefined>;
  updatePlayer(id: number, updates: Partial<GamePlayer>): Promise<GamePlayer | undefined>;
  removePlayerFromArena(id: number): Promise<boolean>;

  // Game Team methods
  createTeam(team: InsertGameTeam): Promise<GameTeam>;
  getTeamsInArena(arenaId: number): Promise<GameTeam[]>;
  getTeamById(id: number): Promise<GameTeam | undefined>;
  updateTeam(id: number, updates: Partial<GameTeam>): Promise<GameTeam | undefined>;

  // Level progression methods
  incrementEnemiesDefeated(arenaId: number, amount: number): Promise<number>;
  advanceLevel(arenaId: number): Promise<GameArena | undefined>;

  // Add a new method to get players in a team
  getPlayersInTeam(teamId: number): Promise<GamePlayer[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private highScores: Map<number, HighScore>;
  private gameArenas: Map<number, GameArena>;
  private gamePlayers: Map<number, GamePlayer>;
  private gameTeams: Map<number, GameTeam>;

  private userCurrentId: number;
  private scoreCurrentId: number;
  private arenaCurrentId: number;
  private playerCurrentId: number;
  private teamCurrentId: number;

  constructor() {
    this.users = new Map();
    this.highScores = new Map();
    this.gameArenas = new Map();
    this.gamePlayers = new Map();
    this.gameTeams = new Map();

    this.userCurrentId = 1;
    this.scoreCurrentId = 1;
    this.arenaCurrentId = 1;
    this.playerCurrentId = 1;
    this.teamCurrentId = 1;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userCurrentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // High Score methods
  async getHighScores(gameMode?: GameMode, limit: number = 10): Promise<HighScore[]> {
    // Get all high scores, filter by game mode if provided, and sort by score in descending order
    return Array.from(this.highScores.values())
      .filter(score => !gameMode || score.gameMode === gameMode)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  async saveHighScore(
    playerName: string,
    score: number,
    gameMode: GameMode = GameMode.SinglePlayer,
    teamName?: string
  ): Promise<HighScore> {
    const id = this.scoreCurrentId++;
    const createdAt = new Date();

    const highScore: HighScore = {
      id,
      playerName,
      score,
      gameMode,
      teamName: teamName || null,
      createdAt
    };

    this.highScores.set(id, highScore);
    return highScore;
  }

  // Game Arena methods
  async createGameArena(arena: InsertGameArena): Promise<GameArena> {
    const id = this.arenaCurrentId++;
    const now = new Date();

    const gameArena: GameArena = {
      id,
      name: arena.name,
      mode: arena.mode,
      maxPlayers: arena.maxPlayers || 4,
      createdAt: now,
      startedAt: null,
      endedAt: null,
      currentLevel: 1,
      enemiesTotal: 10, // Start with 10 enemies as specified
      enemiesDefeated: 0,
      status: 'waiting',
      settings: arena.settings || {}
    };

    this.gameArenas.set(id, gameArena);
    console.log(`Created game arena with ID=${id}, name=${arena.name}, mode=${arena.mode}`);
    return gameArena;
  }

  async getGameArena(id: number): Promise<GameArena | undefined> {
    console.log(`[Storage] Getting arena with ID: ${id} (type=${typeof id})`);

    // Ensure the ID is a number
    const numericId = parseInt(String(id), 10);
    console.log(`[Storage] Using numeric ID: ${numericId} (original: ${id})`);

    // Get all arenas to debug
    const allArenaIds = Array.from(this.gameArenas.keys());
    console.log(`[Storage] Available arena IDs: ${allArenaIds.join(', ')}`);

    const arena = this.gameArenas.get(numericId);
    console.log(`[Storage] Arena ${numericId} found:`, arena ? 'Yes' : 'No');

    if (arena) {
      console.log(`[Storage] Arena details:`, {
        id: arena.id,
        name: arena.name,
        mode: arena.mode,
        status: arena.status
      });
    } else {
      console.error(`[Storage] Arena ${numericId} not found! Available arenas: ${allArenaIds.join(', ')}`);
    }

    return arena;
  }

  async getAllGameArenas(status?: string): Promise<GameArena[]> {
    console.log(`[Storage] Getting all arenas${status ? ` with status: ${status}` : ''}`);
    const arenas = Array.from(this.gameArenas.values());
    const filteredArenas = status ? arenas.filter(a => a.status === status) : arenas;
    console.log(`[Storage] Found ${filteredArenas.length} arenas`);

    // Log all arena IDs for debugging
    console.log(`[Storage] Arena IDs:`, filteredArenas.map(a => a.id));

    return filteredArenas;
  }

  async updateGameArena(id: number, updates: Partial<GameArena>): Promise<GameArena | undefined> {
    const arena = this.gameArenas.get(id);
    if (!arena) return undefined;

    const updatedArena = { ...arena, ...updates };
    this.gameArenas.set(id, updatedArena);
    return updatedArena;
  }

  async deleteGameArena(id: number): Promise<boolean> {
    return this.gameArenas.delete(id);
  }

  // Game Player methods
  async addPlayerToArena(player: InsertGamePlayer): Promise<GamePlayer> {
    const id = this.playerCurrentId++;
    const now = new Date();

    // Generate random spawn position
    const spawnX = (Math.random() - 0.5) * 50;
    const spawnY = 5; // Fixed height for spawning
    const spawnZ = (Math.random() - 0.5) * 50;

    const gamePlayer: GamePlayer = {
      id,
      arenaId: player.arenaId,
      playerId: player.playerId,
      playerName: player.playerName,
      teamId: player.teamId || null,
      isHost: player.isHost || false,
      score: 0,
      spectersCaptured: 0,
      joinedAt: now,
      lastActive: now,
      status: 'active',
      positionX: spawnX,
      positionY: spawnY,
      positionZ: spawnZ
    };

    this.gamePlayers.set(id, gamePlayer);
    return gamePlayer;
  }

  async getPlayersInArena(arenaId: number): Promise<GamePlayer[]> {
    return Array.from(this.gamePlayers.values())
      .filter(player => player.arenaId === arenaId);
  }

  async getPlayerById(id: number): Promise<GamePlayer | undefined> {
    return this.gamePlayers.get(id);
  }

  async getPlayerBySessionId(sessionId: string): Promise<GamePlayer | undefined> {
    return Array.from(this.gamePlayers.values())
      .find(player => player.playerId === sessionId);
  }

  async updatePlayer(id: number, updates: Partial<GamePlayer>): Promise<GamePlayer | undefined> {
    const player = this.gamePlayers.get(id);
    if (!player) return undefined;

    // Update last active timestamp automatically
    updates.lastActive = new Date();

    const updatedPlayer = { ...player, ...updates };
    this.gamePlayers.set(id, updatedPlayer);
    return updatedPlayer;
  }

  async removePlayerFromArena(id: number): Promise<boolean> {
    return this.gamePlayers.delete(id);
  }

  // Game Team methods
  async createTeam(team: InsertGameTeam): Promise<GameTeam> {
    const id = this.teamCurrentId++;

    const gameTeam: GameTeam = {
      id,
      arenaId: team.arenaId,
      name: team.name,
      color: team.color,
      score: 0,
      spectersCaptured: 0
    };

    this.gameTeams.set(id, gameTeam);
    return gameTeam;
  }

  async getTeamsInArena(arenaId: number): Promise<GameTeam[]> {
    return Array.from(this.gameTeams.values())
      .filter(team => team.arenaId === arenaId);
  }

  async getTeamById(id: number): Promise<GameTeam | undefined> {
    return this.gameTeams.get(id);
  }

  async updateTeam(id: number, updates: Partial<GameTeam>): Promise<GameTeam | undefined> {
    const team = this.gameTeams.get(id);
    if (!team) return undefined;

    const updatedTeam = { ...team, ...updates };
    this.gameTeams.set(id, updatedTeam);
    return updatedTeam;
  }

  // Level progression methods
  async incrementEnemiesDefeated(arenaId: number, amount: number = 1): Promise<number> {
    const arena = this.gameArenas.get(arenaId);
    if (!arena) return 0;

    arena.enemiesDefeated += amount;
    this.gameArenas.set(arenaId, arena);
    return arena.enemiesDefeated;
  }

  async advanceLevel(arenaId: number): Promise<GameArena | undefined> {
    const arena = this.gameArenas.get(arenaId);
    if (!arena) return undefined;

    arena.currentLevel += 1;

    // Calculate base enemies for first level
    const baseEnemies = 10;

    // Calculate enemies for current level (10% increase per level)
    const enemiesForLevel = Math.floor(baseEnemies * Math.pow(1.1, arena.currentLevel - 1));

    // Add boss every 10 levels
    const hasBoss = arena.currentLevel % 10 === 0;

    arena.enemiesTotal = enemiesForLevel + (hasBoss ? 1 : 0);
    arena.enemiesDefeated = 0;

    // Update the arena
    this.gameArenas.set(arenaId, arena);
    arena.enemiesTotal = Math.round(arena.enemiesTotal * 1.1);
    arena.enemiesDefeated = 0;

    this.gameArenas.set(arenaId, arena);
    return arena;
  }

  // Add a new method to get players in a team
  async getPlayersInTeam(teamId: number): Promise<GamePlayer[]> {
    return Array.from(this.gamePlayers.values())
      .filter(player => player.teamId === teamId && player.status === 'active');
  }
}

import { PostgresStorage } from './postgresStorage.js';
import { testConnection } from './db.js';

// Create a storage instance based on environment
let storageInstance: IStorage;

// Check if we should use PostgreSQL or fallback to MemStorage
async function initializeStorage(): Promise<IStorage> {
  // Try to connect to the database
  const dbConnected = await testConnection();

  if (dbConnected) {
    console.log('Using PostgreSQL storage');
    return new PostgresStorage();
  } else {
    console.warn('Database connection failed, falling back to in-memory storage');
    return new MemStorage();
  }
}

// Initialize with MemStorage first, then switch to PostgreSQL if available
storageInstance = new MemStorage();

// Try to initialize PostgreSQL storage
initializeStorage().then(storage => {
  storageInstance = storage;
});

export const storage = storageInstance;