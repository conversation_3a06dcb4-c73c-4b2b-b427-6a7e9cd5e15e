import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage.js";
import { z } from "zod";
import { GameMode, insertGameArenaSchema, insertGameTeamSchema } from "@shared/schema.js";
import { setupUnifiedWebSocketServer } from "./unifiedWebSocket.js";
import petApiRouter from "./petApi.js";
import aiApiRouter from "./aiApi.js";
import tournamentBattleRouter from "./src/routes/tournamentBattleRoutes.js";
import nftApiRouter from "./nftApi.js";

// Validation schemas
const scoreParamsSchema = z.object({
  gameMode: z.enum([GameMode.SinglePlayer, GameMode.CoOp, GameMode.PvP]).optional(),
  limit: z.string().transform(Number).pipe(z.number().int().positive()).optional()
});

const saveScoreSchema = z.object({
  playerName: z.string().min(1).max(50),
  score: z.number().int().nonnegative(),
  gameMode: z.enum([GameMode.SinglePlayer, GameMode.CoOp, GameMode.PvP]).optional(),
  teamName: z.string().optional()
});

// Single unified WebSocket server for all connections
let unifiedWebSocketServer: any;

// Export the WebSocket server for global access
export { unifiedWebSocketServer };

export async function registerRoutes(app: Express): Promise<Server> {
  // Create HTTP server
  const httpServer = createServer(app);

  // Initialize unified WebSocket server with the HTTP server
  try {
    console.log('Initializing unified WebSocket server...');
    unifiedWebSocketServer = setupUnifiedWebSocketServer(httpServer);
    console.log('Unified WebSocket server initialized successfully');

    // Make the WebSocket server globally accessible
    (global as any).unifiedWebSocketServer = unifiedWebSocketServer;
  } catch (error) {
    console.error('Failed to initialize unified WebSocket server:', error);
    // Continue even if the WebSocket server fails to initialize
    // This ensures the rest of the application can still function
  }

  // API Routes

  // Pet Specters and NFT API
  app.use('/api/pets', petApiRouter);

  // AI Image Generation API
  app.use('/api/ai', aiApiRouter);

  // Tournament Battle API
  app.use('/api/tournament-battles', tournamentBattleRouter);

  // NFT Metadata API
  app.use('/nft', nftApiRouter);

  // High Scores API

  // Get high scores with optional filtering
  app.get("/api/scores", async (req, res) => {
    try {
      const queryParams = scoreParamsSchema.safeParse(req.query);

      if (!queryParams.success) {
        return res.status(400).json({ error: "Invalid query parameters", details: queryParams.error.format() });
      }

      const { gameMode, limit } = queryParams.data;
      const scores = await storage.getHighScores(gameMode, limit);
      res.json(scores);
    } catch (error) {
      console.error("Error fetching scores:", error);
      res.status(500).json({ error: "Failed to fetch high scores" });
    }
  });

  // Save high score
  app.post("/api/scores", async (req, res) => {
    try {
      const scoreData = saveScoreSchema.safeParse(req.body);

      if (!scoreData.success) {
        return res.status(400).json({ error: "Invalid score data", details: scoreData.error.format() });
      }

      const { playerName, score, gameMode, teamName } = scoreData.data;
      const newScore = await storage.saveHighScore(playerName, score, gameMode, teamName);
      res.status(201).json(newScore);
    } catch (error) {
      console.error("Error saving score:", error);
      res.status(500).json({ error: "Failed to save score" });
    }
  });

  // Game Arenas API

  // Get all game arenas
  app.get("/api/arenas", async (req, res) => {
    try {
      const status = req.query.status as string | undefined;
      console.log(`[API] Getting all arenas with status: ${status || 'any'}`);

      const arenasFromStorage = await storage.getAllGameArenas(status);

      // Augment with live player counts
      const arenasWithLiveCounts = await Promise.all(arenasFromStorage.map(async (arena) => {
        // Get players from both storage and live connections for accurate count
        const storedPlayers = await storage.getPlayersInArena(arena.id);
        const liveCount = unifiedWebSocketServer ? unifiedWebSocketServer.getArenaPlayerCount?.(arena.id) || 0 : 0;

        // Use the higher of the two counts to account for potential sync issues
        const playerCount = Math.max(
          storedPlayers.filter(p => p.status === 'active').length,
          liveCount
        );

        return {
          ...arena,
          playerCount
        };
      }));

      console.log(`[API] Found ${arenasWithLiveCounts.length} arenas. Sending with accurate player counts.`);

      res.json(arenasWithLiveCounts);
    } catch (error) {
      console.error("Error fetching arenas:", error);
      res.status(500).json({ error: "Failed to fetch game arenas" });
    }
  });

  // Get a specific arena
  app.get("/api/arenas/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log(`[API] Request for arena with ID: ${id} (type: ${typeof id})`);

      if (isNaN(id)) {
        console.error(`[API] Invalid arena ID: ${req.params.id}`);
        return res.status(400).json({ error: "Invalid arena ID" });
      }

      const arena = await storage.getGameArena(id);
      if (!arena) {
        console.error(`[API] Arena ${id} not found`);
        return res.status(404).json({ error: "Arena not found" });
      }

      console.log(`[API] Arena ${id} found and returned to client (${arena.name})`);
      res.json(arena);
    } catch (error) {
      console.error("Error fetching arena:", error);
      res.status(500).json({ error: "Failed to fetch game arena" });
    }
  });

  // Create new game arena
  app.post("/api/arenas", async (req, res) => {
    try {
      const arenaData = insertGameArenaSchema.safeParse(req.body);

      if (!arenaData.success) {
        console.error("[API] Invalid arena data:", arenaData.error.format());
        return res.status(400).json({ error: "Invalid arena data", details: arenaData.error.format() });
      }

      console.log("[API] Creating new arena:", arenaData.data);
      const newArena = await storage.createGameArena(arenaData.data);
      console.log(`[API] Arena created successfully with ID: ${newArena.id}`);

      res.status(201).json(newArena);
    } catch (error) {
      console.error("Error creating arena:", error);
      res.status(500).json({ error: "Failed to create game arena" });
    }
  });

  // Update an existing arena
  app.patch("/api/arenas/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: "Invalid arena ID" });
      }

      const arena = await storage.getGameArena(id);
      if (!arena) {
        return res.status(404).json({ error: "Arena not found" });
      }

      const updatedArena = await storage.updateGameArena(id, req.body);
      res.json(updatedArena);
    } catch (error) {
      console.error("Error updating arena:", error);
      res.status(500).json({ error: "Failed to update game arena" });
    }
  });

  // Delete a game arena
  app.delete("/api/arenas/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: "Invalid arena ID" });
      }

      const deleted = await storage.deleteGameArena(id);
      if (!deleted) {
        return res.status(404).json({ error: "Arena not found" });
      }

      res.status(204).end();
    } catch (error) {
      console.error("Error deleting arena:", error);
      res.status(500).json({ error: "Failed to delete game arena" });
    }
  });

  // Teams API

  // Get teams in an arena
  app.get("/api/arenas/:arenaId/teams", async (req, res) => {
    try {
      const arenaId = parseInt(req.params.arenaId);
      if (isNaN(arenaId)) {
        return res.status(400).json({ error: "Invalid arena ID" });
      }

      const teams = await storage.getTeamsInArena(arenaId);
      res.json(teams);
    } catch (error) {
      console.error("Error fetching teams:", error);
      res.status(500).json({ error: "Failed to fetch teams" });
    }
  });

  // Create a team in an arena
  app.post("/api/arenas/:arenaId/teams", async (req, res) => {
    try {
      const arenaId = parseInt(req.params.arenaId);
      if (isNaN(arenaId)) {
        return res.status(400).json({ error: "Invalid arena ID" });
      }

      const arena = await storage.getGameArena(arenaId);
      if (!arena) {
        return res.status(404).json({ error: "Arena not found" });
      }

      const teamData = insertGameTeamSchema.safeParse({
        ...req.body,
        arenaId
      });

      if (!teamData.success) {
        return res.status(400).json({ error: "Invalid team data", details: teamData.error.format() });
      }

      const newTeam = await storage.createTeam(teamData.data);
      res.status(201).json(newTeam);
    } catch (error) {
      console.error("Error creating team:", error);
      res.status(500).json({ error: "Failed to create team" });
    }
  });

  // Get players in an arena
  app.get("/api/arenas/:arenaId/players", async (req, res) => {
    try {
      const arenaId = parseInt(req.params.arenaId);
      if (isNaN(arenaId)) {
        return res.status(400).json({ error: "Invalid arena ID" });
      }

      const players = await storage.getPlayersInArena(arenaId);
      res.json(players);
    } catch (error) {
      console.error("Error fetching players:", error);
      res.status(500).json({ error: "Failed to fetch players" });
    }
  });

  // Utility endpoint to check server status
  app.get("/api/status", (req, res) => {
    res.json({ status: "ok", time: new Date().toISOString() });
  });

  return httpServer;
}