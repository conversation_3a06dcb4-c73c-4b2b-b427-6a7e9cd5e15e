import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { db, pool } from './db.js';

// Run migrations
async function runMigrations() {
  console.log('Running database migrations...');

  try {
    // Run migrations
    await migrate(db, { migrationsFolder: './migrations' });
    console.log('✅ Migrations completed successfully');

    // Only close the pool if running as a standalone script
    if (process.argv[1] === import.meta.url) {
      await pool.end();
    }

    return true;
  } catch (error) {
    console.error('❌ Migration failed:', error);

    // Only close the pool if running as a standalone script
    if (process.argv[1] === import.meta.url) {
      await pool.end();
    }

    return false;
  }
}

// Run migrations if this file is executed directly
if (process.argv[1] === import.meta.url) {
  runMigrations().then(success => {
    process.exit(success ? 0 : 1);
  });
}

export { runMigrations };
