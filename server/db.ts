import { drizzle } from 'drizzle-orm/node-postgres';
import pg from 'pg';
import * as schema from '../shared/schema.js';
import * as petSchema from '../shared/petSchema.js';

const { Pool } = pg;

// Check for required environment variable
if (!process.env.DATABASE_URL) {
  console.warn('DATABASE_URL environment variable not set. Using default connection string for development.');
}

// Connection string
const connectionString = process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/spectershift';

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString,
});

// Create a Drizzle ORM instance with the connection pool and schema
export const db = drizzle(pool, { schema: { ...schema, ...petSchema } });

// Function to create a new connection pool
export function createNewPool() {
  return new Pool({
    connectionString,
  });
}

// Function to create a new Drizzle ORM instance
export function createNewDb() {
  const newPool = createNewPool();
  return {
    db: drizzle(newPool, { schema: { ...schema, ...petSchema } }),
    pool: newPool
  };
}

// Export a function to test the database connection
export async function testConnection() {
  try {
    // Simple query to test the connection
    await pool.query('SELECT NOW()');
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Export the pool for potential direct usage
export { pool };
