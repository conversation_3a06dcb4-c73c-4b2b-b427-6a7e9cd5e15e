import express from 'express';
import { AIImageService } from './aiImageService.js';
import { LLMService, requestQueue } from './llmService.js';
import { z } from 'zod';

// Create router
const router = express.Router();

// Validation schemas
const generateImageSchema = z.object({
  prompt: z.string().optional(),
  specterType: z.string(),
  description: z.string().optional(),
  color: z.string().optional(),
  style: z.string().optional(),
  bypassCache: z.boolean().optional()
});

const enhancePromptSchema = z.object({
  specterType: z.string(),
  description: z.string(),
  color: z.string().optional(),
  style: z.string().optional(),
  requestId: z.string().uuid().optional() // For retrieving cached results
});

const analyzeNFTImageSchema = z.object({
  imageUrl: z.string().url(),
  requestId: z.string().uuid().optional() // For retrieving cached results
});

// Schema for request status validation - used in the request-status/:requestId route

// Generate an image based on preferences
router.post('/generate-image', async (req, res) => {
  try {
    const validatedData = generateImageSchema.parse(req.body);

    // Get user ID from request (use IP address if no user ID is available)
    const userId = req.headers['x-user-id'] as string || req.ip || 'anonymous';

    console.log('Received image generation request:', validatedData);

    // If a prompt is provided directly, use it instead of generating one
    const description = validatedData.prompt || validatedData.description;
    if (validatedData.prompt) {
      console.log('Using provided LLM-enhanced prompt:', validatedData.prompt);
    }

    // Generate the image
    const result = await AIImageService.generatePetSpecterImage({
      specterType: validatedData.specterType,
      description: description,
      color: validatedData.color,
      style: validatedData.style
    }, userId, validatedData.bypassCache);

    res.json({
      imageUrl: result.url,
      prompt: result.prompt
    });
  } catch (error) {
    console.error('Error generating image:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to generate image' });
  }
});

// Enhance a prompt with LLM
router.post('/enhance-prompt', async (req, res) => {
  try {
    const validatedData = enhancePromptSchema.parse(req.body);

    // Get user ID from request
    const userId = req.headers['x-user-id'] as string || req.ip || 'anonymous';

    // Check if this is a request to retrieve a cached result
    if (validatedData.requestId) {
      // Get the request status
      const status = requestQueue.getRequestStatus(validatedData.requestId);

      // If the request is completed, return the cached result
      if (status && status.status === 'completed') {
        // Get the cached result
        const cacheKey = LLMService.createCacheKeyForRequestId(validatedData.requestId);
        return res.json({ enhancedPrompt: cacheKey });
      }

      // If the request is not completed, return an error
      return res.status(400).json({ error: 'Request not completed yet' });
    }

    // Enhance the prompt
    const result = await LLMService.enhancePromptForPetGeneration(validatedData, userId);

    // If in queue, return queue position
    if (result.queuePosition > 0) {
      return res.status(202).json({
        message: 'Request queued',
        queuePosition: result.queuePosition,
        requestId: result.requestId
      });
    }

    // Otherwise return the enhanced prompt
    res.json({ enhancedPrompt: result.enhancedPrompt });
  } catch (error) {
    console.error('Error enhancing prompt:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to enhance prompt' });
  }
});

// Analyze an NFT image
router.post('/analyze-nft-image', async (req, res) => {
  try {
    const validatedData = analyzeNFTImageSchema.parse(req.body);

    // Get user ID from request
    const userId = req.headers['x-user-id'] as string || req.ip || 'anonymous';

    // Check if this is a request to retrieve a cached result
    if (validatedData.requestId) {
      // Get the request status
      const status = requestQueue.getRequestStatus(validatedData.requestId);

      // If the request is completed, return the cached result
      if (status && status.status === 'completed') {
        // Get the cached result
        const cacheKey = LLMService.createCacheKeyForRequestId(validatedData.requestId);
        return res.json(cacheKey);
      }

      // If the request is not completed, return an error
      return res.status(400).json({ error: 'Request not completed yet' });
    }

    // Analyze the image
    const result = await LLMService.analyzeNFTImage(validatedData.imageUrl, userId);

    // If in queue, return queue position
    if (result.queuePosition > 0) {
      return res.status(202).json({
        message: 'Request queued',
        queuePosition: result.queuePosition,
        requestId: result.requestId
      });
    }

    // Otherwise return the analysis
    res.json(result.analysis);
  } catch (error) {
    console.error('Error analyzing NFT image:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to analyze NFT image' });
  }
});

// Get request status
router.get('/request-status/:requestId', async (req, res) => {
  try {
    const { requestId } = req.params;

    // Validate request ID
    try {
      z.string().uuid().parse(requestId);
    } catch (error) {
      return res.status(400).json({ error: 'Invalid request ID format' });
    }

    // Get request status from queue
    const status = requestQueue.getRequestStatus(requestId);

    if (!status) {
      return res.status(404).json({ error: 'Request not found' });
    }

    // Get position in queue
    const queuePosition = requestQueue.getPositionInQueue(requestId);

    res.json({
      status: status.status,
      queuePosition,
      type: status.type,
      timestamp: status.timestamp
    });
  } catch (error) {
    console.error('Error getting request status:', error);
    res.status(500).json({ error: 'Failed to get request status' });
  }
});

// Get all requests in queue
router.get('/queue', async (_req, res) => {
  try {
    // Get all requests in queue
    const requests = requestQueue.getAllRequests();

    res.json({
      queueLength: requests.length,
      requests: requests.map(req => ({
        id: req.id,
        status: req.status,
        type: req.type,
        timestamp: req.timestamp
      }))
    });
  } catch (error) {
    console.error('Error getting queue:', error);
    res.status(500).json({ error: 'Failed to get queue' });
  }
});

export default router;
