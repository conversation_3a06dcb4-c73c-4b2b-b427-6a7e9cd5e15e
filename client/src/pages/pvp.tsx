import React, { useEffect, useState, useRef } from 'react';
import { useLocation } from 'wouter';
import GameEngine from '@/game/engine/GameEngine.js';
import { useToast } from '@/hooks/use-toast.js';
import { Loader2 } from 'lucide-react';
import { useTournamentBattleConnection } from '@/hooks/useTournamentBattleConnection.js';
import { usePvpAccess } from '@/contexts/PvpAccessContext.jsx';
import { Button } from '@/components/ui/button.jsx';

const PVPArena: React.FC = () => {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const gameContainerRef = useRef<HTMLDivElement>(null);
  const gameEngineRef = useRef<GameEngine | null>(null);
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const { isPvpEnabled } = usePvpAccess();
  const [battleId, setBattleId] = useState<string | null>(null);
  const [arenaInitialized, setArenaInitialized] = useState<boolean>(false);
  const initializeAttemptedRef = useRef<boolean>(false);
  const lastConnectionAttemptRef = useRef<number>(0);

  const tournamentConnection = useTournamentBattleConnection({
    autoConnect: true,
    onConnect: (connectionId) => {
      console.log(`Tournament Battle: Connected to game server with ID: ${connectionId}`);
      setConnectionId(connectionId);
      toast({
        title: 'Connected',
        description: 'Connected to tournament battle server',
        duration: 3000
      });

      if (battleId && !arenaInitialized && !initializeAttemptedRef.current) {
        console.log('Tournament Battle: Connection established, now joining battle as spectator');
        initializeAttemptedRef.current = true;

        tournamentConnection.joinTournamentBattle(battleId);

        if (gameEngineRef.current) {
          console.log('Tournament Battle: Telling game engine to connect to battle...');
          gameEngineRef.current.connectToBattle(battleId, true);
          setTimeout(() => setArenaInitialized(true), 1000);
        }
      }
    },
    onDisconnect: () => {
      console.log('Tournament Battle: Disconnected from game server');
      toast({
        title: 'Disconnected',
        description: 'Lost connection to tournament battle server',
        duration: 3000,
        variant: 'destructive'
      });

      if (window.location.pathname.includes('/pvp')) {
        const now = Date.now();
        if (now - lastConnectionAttemptRef.current > 5000) {
          lastConnectionAttemptRef.current = now;
          console.log('Attempting to reconnect to tournament battle server...');
          setTimeout(() => tournamentConnection.connect(), 2000);
        }
      }
    },
    onBattleUpdate: (data) => {
      console.log('Tournament Battle: Received battle update:', data);
      if (gameEngineRef.current) {
        gameEngineRef.current.processTournamentBattleUpdate(data);
      }
    },
    onRenderInstructions: (data) => {
      console.log('Tournament Battle: Received render instructions:', data);
      if (gameEngineRef.current) {
        gameEngineRef.current.processTournamentRenderInstructions(data);
      }
    },
    onError: (error) => {
      console.error('Tournament Battle: Error:', error);
      toast({
        title: 'Error',
        description: error,
        duration: 5000,
        variant: 'destructive'
      });
    }
  });

  useEffect(() => {
    // Extract battleId from URL immediately on mount
    const urlParams = new URLSearchParams(window.location.search);
    const queryBattleId = urlParams.get('battleId');
    if (queryBattleId && !battleId) {
      console.log('[Mount Effect] Setting Battle ID from URL:', queryBattleId);
      setBattleId(queryBattleId);
    }
    
    if (!isPvpEnabled) {
      setError('Access Denied: You do not have permission to access the PVP Arena.');
      setIsLoading(false);
      return;
    }

    const initializePVPArena = async () => {
      // Prevent multiple initializations
      if (gameEngineRef.current || initializeAttemptedRef.current) {
        console.log('Skipping PVP Arena initialization - already initialized or attempted');
        setIsLoading(false);
        return;
      }
      
      console.log('[Initialize] Starting PVP Arena Initialization...');
      initializeAttemptedRef.current = true;
      
      try {
        if (!window.location.pathname.includes('/pvp')) {
          console.log('[Initialize] Not on PVP page - skipping arena initialization');
          return;
        }
        
        if (!gameContainerRef.current) {
          throw new Error('Game container ref not found');
        }

        // 1. Create GameEngine instance (don't start yet)
        console.log('[Initialize] Creating GameEngine instance...');
        const gameEngine = new GameEngine(gameContainerRef.current, false, true); // Set isMobile to false, keep isSpectatorMode true
        gameEngineRef.current = gameEngine;
        console.log('[Initialize] GameEngine instance created.');

        // 2. Determine Battle ID (use state value which might have been set by URL param effect)
        const currentBattleId = battleId || queryBattleId; // Use state or URL param
        
        if (currentBattleId) {
           console.log(`[Initialize] Found Battle ID: ${currentBattleId}. Connecting engine to battle...`);
           // 3. Connect engine to the battle (this sets isPvpArena flag and starts arena creation)
           gameEngine.connectToBattle(currentBattleId, true); 
        } else {
          console.warn('[Initialize] No Battle ID found on initialization. User might need to create a test battle.');
          // Handle case where no battle ID exists - maybe show a button?
          // For now, we proceed to start, but it might default to an empty scene or error
        }

        // 4. Start the engine's animation loop AFTER connectToBattle is called
        console.log('[Initialize] Starting GameEngine animation loop...');
        gameEngine.start();

        console.log('[Initialize] PVP arena initialization sequence completed.');
        
        toast({
          title: 'Joined PVP Arena',
          description: `Spectator mode active. Use WASD to move.`, // Simplified message
          duration: 5000
        });

        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing PVP Arena:', error);
        setError('Failed to initialize PVP Arena. Please try again.');
        setIsLoading(false);
      }
    };

    // Connect WebSocket if needed
    if (!connectionId) {
      console.log('[Mount Effect] Connecting WebSocket...');
      tournamentConnection.connect(); // Connect attempt
    }

    // Initialize the Arena (which now includes starting the engine)
    initializePVPArena();

    // Cleanup function
    return () => {
      if (!window.location.pathname.includes('/pvp')) {
        console.log('Tournament Battle: Leaving PVP page, disconnecting WebSocket...');
        tournamentConnection.disconnect();
      } else {
        console.log('Tournament Battle: Still on PVP page, keeping WebSocket connections active');
      }

      if (gameEngineRef.current && !window.location.pathname.includes('/pvp')) {
        gameEngineRef.current.dispose();
        gameEngineRef.current = null;
      }
    };
  }, []);

  const createTestBattle = async () => {
    try {
      const now = Date.now();
      if (now - lastConnectionAttemptRef.current < 5000) {
        console.log('Rate limiting test battle creation');
        return;
      }
      lastConnectionAttemptRef.current = now;
      
      setArenaInitialized(false);
      initializeAttemptedRef.current = false;
      
      const testBattleId = crypto.randomUUID();
      console.log('Created test battle with ID:', testBattleId);
      
      setBattleId(testBattleId);
      
      const newUrl = `/pvp?battleId=${testBattleId}`;
      window.history.pushState({}, '', newUrl);
      
      toast({
        title: 'Test Battle Created',
        description: `Created test battle with ID: ${testBattleId}`,
        duration: 5000
      });
      
      if (connectionId && gameEngineRef.current) {
        console.log('Tournament Battle: Connection already established, initializing test arena...');
        tournamentConnection.joinTournamentBattle(testBattleId);
        gameEngineRef.current.connectToBattle(testBattleId, true);
        setTimeout(() => setArenaInitialized(true), 1000);
      }
    } catch (error) {
      console.error('Error creating test battle:', error);
      toast({
        title: 'Error',
        description: 'Failed to create test battle',
        duration: 5000,
        variant: 'destructive'
      });
    }
  };

  const handleGameEngineInit = (engine: GameEngine) => {
    console.log('Game engine initialized for PVP arena');
    gameEngineRef.current = engine;
    
    if (battleId && connectionId && !arenaInitialized && !initializeAttemptedRef.current) {
      console.log('Tournament Battle: Game engine ready, connecting to battle');
      initializeAttemptedRef.current = true;
      tournamentConnection.joinTournamentBattle(battleId);
      engine.connectToBattle(battleId, true);
      setTimeout(() => setArenaInitialized(true), 1000);
    }
  };

  useEffect(() => {
    return () => {
      setArenaInitialized(false);
      initializeAttemptedRef.current = false;
    };
  }, []);

  return (
    <>
      <div className="relative w-full h-screen bg-black">
        {isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-80 z-50">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500 mb-4" />
            <p className="text-white text-lg">Loading PVP Arena...</p>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-80 z-50">
            <div className="bg-red-900 p-6 rounded-lg max-w-md">
              <h2 className="text-white text-xl mb-4">Error</h2>
              <p className="text-white mb-6">{error}</p>
              <button
                onClick={() => setLocation('/')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                Back to Home
              </button>
            </div>
          </div>
        )}

        <div ref={gameContainerRef} className="w-full h-full" />

        <div className="absolute bottom-4 right-4 bg-black bg-opacity-70 p-4 rounded-lg text-white">
          <h3 className="font-bold">Joined PVP Arena</h3>
          <p className="text-sm">You are now in the Coliseum Arena as a spectator.</p>
          <p className="text-sm">Use WASD to move around and get a better view of the battles.</p>
        </div>
      </div>

      <div className="absolute top-4 right-4 flex flex-col gap-2">
        <Button
          variant="outline"
          onClick={() => setLocation('/')}
        >
          Back to Home
        </Button>
        
        <Button
          variant="outline"
          onClick={createTestBattle}
        >
          Create New Test Battle
        </Button>
      </div>
    </>
  );
};

export default PVPArena;
