import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { But<PERSON> } from '@/components/ui/button.jsx';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card.jsx';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs.jsx';
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog.jsx';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Textarea } from '@/components/ui/textarea.jsx';
import { ArrowLeft, CheckCircle2, ExternalLink, Mail } from 'lucide-react';
import { useToast } from '@/hooks/use-toast.js';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// Form schema
const inquiryFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  company: z.string().min(1, { message: 'Company name is required.' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters.' }),
  planType: z.string().optional(),
});

const Sponsors = () => {
  const [, setLocation] = useLocation();
  const [showInquiryDialog, setShowInquiryDialog] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<z.infer<typeof inquiryFormSchema>>({
    resolver: zodResolver(inquiryFormSchema),
    defaultValues: {
      name: '',
      email: '',
      company: '',
      message: '',
      planType: '',
    },
  });

  const handleContactClick = (planType: string) => {
    form.setValue('planType', planType);
    form.setValue('message', `I'm interested in the ${planType} sponsorship package. Please provide more details.`);
    setShowInquiryDialog(true);
  };

  const onSubmit = (values: z.infer<typeof inquiryFormSchema>) => {
    // Redirect to email client with pre-filled details
    const subject = `SpecterShift Sponsorship Inquiry - ${values.planType} Package`;
    const body = `Name: ${values.name}
Company: ${values.company}
Email: ${values.email}

${values.message}`;
    
    window.location.href = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    
    setShowInquiryDialog(false);
    form.reset();
  };

  const placements = [
    {
      name: "Home Page Side Banners",
      description: "Banner placements on the sides of the main menu screen."
    },
    {
      name: "Home Page Top Banner",
      description: "Premium placement at the top of the main menu screen."
    },
    {
      name: "In-Game Billboards",
      description: "Digital billboards and posters within the game environment."
    },
    {
      name: "Pet Specter Skins",
      description: "Branded skins for pet specters that follow players."
    },
    {
      name: "MerchGenie Kiosk",
      description: "Featured placement in the virtual merchandise store."
    },
    {
      name: "Interactive Business Kiosk",
      description: "In-game interactive kiosk that showcases your business details."
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-900 via-indigo-900 to-black overflow-y-auto">
      <div className="container mx-auto px-4 py-8">
        {/* Back button */}
        <Button 
          variant="ghost" 
          className="mb-6 text-white hover:bg-white/10"
          onClick={() => setLocation('/')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Button>

        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
              Sponsor <span className="text-blue-400">SpecterShift</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Connect your brand with our growing community of gamers in the exciting world of SpecterShift Hunters.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <Card className="bg-gray-900 border-blue-400/30 text-white hover:border-blue-400 transition-all">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl text-blue-400">Basic</CardTitle>
                <div className="text-3xl font-bold">$100<span className="text-lg font-normal text-gray-400">/month</span></div>
                <CardDescription className="text-gray-400">Perfect for indie developers and small businesses</CardDescription>
              </CardHeader>
              <CardContent className="pb-4">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>Home page side banners</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  onClick={() => handleContactClick('Basic')}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Us
                </Button>
              </CardFooter>
            </Card>

            <Card className="bg-gray-900 border-blue-400 text-white relative overflow-hidden hover:shadow-blue-400/20 hover:shadow-lg transition-all">
              <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs font-bold py-1 px-3 rounded-bl-lg">
                POPULAR
              </div>
              <CardHeader className="pb-4">
                <CardTitle className="text-xl text-blue-400">Premium</CardTitle>
                <div className="text-3xl font-bold">$250<span className="text-lg font-normal text-gray-400">/month</span></div>
                <CardDescription className="text-gray-400">Ideal for established studios and medium businesses</CardDescription>
              </CardHeader>
              <CardContent className="pb-4">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>1 in-game billboard placement</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>Custom Pet Specter skin with your logo</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>Featured in MerchGenie kiosk</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  onClick={() => handleContactClick('Premium')}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Us
                </Button>
              </CardFooter>
            </Card>

            <Card className="bg-gray-900 border-purple-400/30 text-white hover:border-purple-400 transition-all">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl text-purple-400">Enterprise</CardTitle>
                <div className="text-3xl font-bold">$500<span className="text-lg font-normal text-gray-400">/month</span></div>
                <CardDescription className="text-gray-400">For major brands and publishers</CardDescription>
              </CardHeader>
              <CardContent className="pb-4">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>Top banner placement on home page</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>2 in-game billboard placements</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>Custom branded game items</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 shrink-0 mt-0.5" />
                    <span>Interactive in-game kiosk about your business</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full bg-purple-600 hover:bg-purple-700"
                  onClick={() => handleContactClick('Enterprise')}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Us
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="mb-12">
            <h2 className="text-2xl font-bold text-blue-400 mb-6 text-center">Sponsor Placement Options</h2>
            <Tabs defaultValue="list" className="w-full">
              <TabsList className="w-full max-w-md mx-auto grid grid-cols-2 mb-6 bg-gray-800">
                <TabsTrigger value="list">List View</TabsTrigger>
                <TabsTrigger value="grid">Grid View</TabsTrigger>
              </TabsList>
              <TabsContent value="list">
                <div className="space-y-4">
                  {placements.map((placement, index) => (
                    <Card key={index} className="bg-gray-900 border-blue-400/30 text-white">
                      <CardHeader className="py-4">
                        <CardTitle className="text-lg">{placement.name}</CardTitle>
                        <CardDescription className="text-gray-400">{placement.description}</CardDescription>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="grid">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {placements.map((placement, index) => (
                    <Card key={index} className="bg-gray-900 border-blue-400/30 text-white">
                      <CardHeader className="py-4">
                        <CardTitle className="text-lg">{placement.name}</CardTitle>
                        <CardDescription className="text-gray-400">{placement.description}</CardDescription>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4">Custom Partnership Opportunities</h2>
            <p className="text-gray-300 mb-6">
              Looking for a more tailored sponsorship solution? We offer custom packages
              designed specifically for your brand's needs and goals.
            </p>
            <Button 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              size="lg"
              onClick={() => handleContactClick('Custom')}
            >
              <Mail className="mr-2 h-5 w-5" />
              Request Custom Package
            </Button>
          </div>
        </div>
      </div>

      {/* Contact Form Dialog */}
      <Dialog open={showInquiryDialog} onOpenChange={setShowInquiryDialog}>
        <DialogContent className="bg-gray-900 border-blue-400 text-white sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-xl text-blue-400">Sponsorship Inquiry</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Your name" className="bg-gray-800 border-gray-700" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" className="bg-gray-800 border-gray-700" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company</FormLabel>
                    <FormControl>
                      <Input placeholder="Your company" className="bg-gray-800 border-gray-700" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Tell us about your sponsorship interests" className="bg-gray-800 border-gray-700 min-h-[100px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter className="pt-2">
                <Button type="button" variant="outline" onClick={() => setShowInquiryDialog(false)} className="border-gray-700">
                  Cancel
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  Send Inquiry
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Footer */}
      <div className="bg-black/50 text-gray-400 py-6 mt-12">
        <div className="container mx-auto px-4 text-center">
          <p className="mb-2">© 2025 SpecterShift Hunters</p>
          <p className="text-sm">
            For direct sponsorship inquiries, contact: 
            <a href="mailto:<EMAIL>" className="text-blue-400 ml-1 hover:underline inline-flex items-center">
              <EMAIL>
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </p>
        </div>
      </div>
      
      {/* Add style override for scrolling */}
      <style>
        {`
          html, body {
            overflow: auto !important;
            height: auto !important;
          }
        `}
      </style>
    </div>
  );
};

export default Sponsors; 