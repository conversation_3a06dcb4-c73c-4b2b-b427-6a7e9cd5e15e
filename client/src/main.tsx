// CRITICAL FIX: Import the unified WebSocket utility first to ensure it captures the native WebSocket
// before any proxying is applied
import "./utils/unifiedWebSocket";

// Set up WalletConnect singleton before any components are rendered
// import { setupWalletConnectSingleton } from './utils/walletConnectSingleton';
// setupWalletConnectSingleton(); // REMOVED: Relying on BedrockPassportProvider for initialization

import React from 'react';
// Import both ReactDOM and ReactDOMClient to ensure we have all methods
import * as ReactDOMClient from 'react-dom/client';
import * as ReactDOM from 'react-dom';
import App from "./App.jsx";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient.js";
import { disableHmrWebSocket } from "./utils/disableHmr.js";

// Disable Vite HMR WebSocket connections to prevent conflicts with game WebSockets
disableHmrWebSocket();

// Expose React and ReactDOM to the window for external scripts like Bedrock Passport
// Ensure all necessary methods are available including createPortal
window.React = React;
window.ReactDOM = {
  ...ReactDOM,
  createRoot: ReactDOMClient.createRoot,
  createPortal: ReactDOM.createPortal
};

console.log('Main: Initialized React and ReactDOM on window object with createPortal:', !!window.ReactDOM.createPortal);

ReactDOMClient.createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <App />
  </QueryClientProvider>
);
