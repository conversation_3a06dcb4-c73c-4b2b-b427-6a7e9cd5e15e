declare module 'three/examples/jsm/controls/PointerLockControls' {
  import { Camera, EventDispatcher } from 'three';

  export class PointerLockControls extends EventDispatcher {
    constructor(camera: Camera, domElement?: HTMLElement);
    isLocked: boolean;
    connect(): void;
    disconnect(): void;
    dispose(): void;
    getObject(): Camera;
    getDirection(v: THREE.Vector3): THREE.Vector3;
    moveForward(distance: number): void;
    moveRight(distance: number): number;
    lock(): void;
    unlock(): void;
    pointerSpeed: number;
  }
}

declare module 'three/examples/jsm/postprocessing/EffectComposer' {
  import { WebGLRenderer, WebGLRenderTarget } from 'three';
  import { Pass } from 'three/examples/jsm/postprocessing/Pass';

  export class EffectComposer {
    constructor(renderer: WebGLRenderer, renderTarget?: WebGLRenderTarget);
    renderer: WebGLRenderer;
    renderTarget1: WebGLRenderTarget;
    renderTarget2: WebGLRenderTarget;
    writeBuffer: WebGLRenderTarget;
    readBuffer: WebGLRenderTarget;
    passes: Pass[];
    copyPass: Pass;
    enabled: boolean;
    renderToScreen: boolean;

    swapBuffers(): void;
    addPass(pass: Pass): void;
    insertPass(pass: Pass, index: number): void;
    removePass(pass: Pass): void;
    render(deltaTime?: number): void;
    reset(renderTarget?: WebGLRenderTarget): void;
    setSize(width: number, height: number): void;
    setPixelRatio(pixelRatio: number): void;
    dispose(): void;
  }
}

declare module 'three/examples/jsm/postprocessing/RenderPass' {
  import { Scene, Camera, Material, Color } from 'three';
  import { Pass } from 'three/examples/jsm/postprocessing/Pass';

  export class RenderPass extends Pass {
    constructor(scene: Scene, camera: Camera, overrideMaterial?: Material, clearColor?: Color | string | number, clearAlpha?: number);
    scene: Scene;
    camera: Camera;
    overrideMaterial: Material;
    clearColor: Color | string | number;
    clearAlpha: number;
    clear: boolean;
    clearDepth: boolean;
  }
}

declare module 'three/examples/jsm/postprocessing/UnrealBloomPass' {
  import { Vector2, WebGLRenderer, Scene, Camera, MeshBasicMaterial, ShaderMaterial } from 'three';
  import { Pass } from 'three/examples/jsm/postprocessing/Pass';

  export class UnrealBloomPass extends Pass {
    constructor(resolution: Vector2, strength: number, radius: number, threshold: number);
    strength: number;
    radius: number;
    threshold: number;
    resolution: Vector2;
    renderTargetsHorizontal: WebGLRenderTarget[];
    renderTargetsVertical: WebGLRenderTarget[];
    nMips: number;
    skipBlur: boolean;
    basic: MeshBasicMaterial;
    glows: ShaderMaterial[];

    dispose(): void;
    setSize(width: number, height: number): void;
  }
}
