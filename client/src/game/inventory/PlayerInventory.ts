import { SpecterEquipment } from '../entities/PetSpecter.js';

/**
 * Manages the player's inventory of purchased items
 */
export class PlayerInventory {
  private static instance: PlayerInventory;
  
  // Store purchased equipment by ID
  private purchasedEquipment: Map<string, SpecterEquipment> = new Map();
  
  // Event for inventory changes
  private inventoryChangeListeners: (() => void)[] = [];
  
  private constructor() {
    // Private constructor for singleton
    this.loadFromLocalStorage();
  }
  
  /**
   * Get the singleton instance
   */
  public static getInstance(): PlayerInventory {
    if (!PlayerInventory.instance) {
      PlayerInventory.instance = new PlayerInventory();
    }
    return PlayerInventory.instance;
  }
  
  /**
   * Add equipment to inventory
   */
  public addEquipment(equipment: SpecterEquipment): void {
    this.purchasedEquipment.set(equipment.id, equipment);
    this.saveToLocalStorage();
    this.notifyInventoryChange();
  }
  
  /**
   * Check if equipment is owned
   */
  public hasEquipment(equipmentId: string): boolean {
    return this.purchasedEquipment.has(equipmentId);
  }
  
  /**
   * Get all purchased equipment
   */
  public getAllEquipment(): SpecterEquipment[] {
    return Array.from(this.purchasedEquipment.values());
  }
  
  /**
   * Get equipment by type
   */
  public getEquipmentByType(type: 'weapon' | 'armor' | 'utility'): SpecterEquipment[] {
    return this.getAllEquipment().filter(item => item.type === type);
  }
  
  /**
   * Add inventory change listener
   */
  public addInventoryChangeListener(listener: () => void): void {
    this.inventoryChangeListeners.push(listener);
  }
  
  /**
   * Remove inventory change listener
   */
  public removeInventoryChangeListener(listener: () => void): void {
    const index = this.inventoryChangeListeners.indexOf(listener);
    if (index !== -1) {
      this.inventoryChangeListeners.splice(index, 1);
    }
  }
  
  /**
   * Notify all listeners of inventory change
   */
  private notifyInventoryChange(): void {
    this.inventoryChangeListeners.forEach(listener => listener());
  }
  
  /**
   * Save inventory to localStorage
   */
  private saveToLocalStorage(): void {
    try {
      const data = JSON.stringify(Array.from(this.purchasedEquipment.entries()));
      localStorage.setItem('playerInventory', data);
    } catch (error) {
      console.error('Failed to save inventory to localStorage:', error);
    }
  }
  
  /**
   * Load inventory from localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const data = localStorage.getItem('playerInventory');
      if (data) {
        const entries = JSON.parse(data) as [string, SpecterEquipment][];
        this.purchasedEquipment = new Map(entries);
      }
    } catch (error) {
      console.error('Failed to load inventory from localStorage:', error);
    }
  }
  
  /**
   * Clear inventory (for testing)
   */
  public clear(): void {
    this.purchasedEquipment.clear();
    this.saveToLocalStorage();
    this.notifyInventoryChange();
  }
}

// Export singleton instance
export const playerInventory = PlayerInventory.getInstance();
