import { AmmoType, SpecterType } from './types.js';

// World settings
export const WORLD_SIZE = 100; // Size of the main ground plane
export const GRAVITY = -65; // Default gravity - Increased further for less floaty feel

// Player settings
export const PLAYER_HEIGHT = 1.8; // Player height in units
export const PLAYER_MASS = 80; // Player mass in kg
export const PLAYER_MOVE_SPEED = 30.0; // Player movement force
export const PLAYER_JUMP_VELOCITY = 12.0;
export const PLAYER_JUMP_FORCE = 15.0; // Added jump force constant
export const PLAYER_JETPACK_FORCE = 50.0; // Increased jetpack thrust force
export const PLAYER_JETPACK_MAX_FUEL = 150; // Increased maximum jetpack fuel
export const PLAYER_JETPACK_REGEN_RATE = 15; // Increased fuel regeneration rate
export const PLAYER_JETPACK_CONSUMPTION_RATE = 20; // Reduced fuel consumption rate
export const PLAYER_MAX_HEALTH = 100; // Maximum player health

// Specter settings
export const SPECTER_MOVE_SPEED = 50;
export const SPECTER_MASS = 10;
export const SPECTER_ATTACK_DAMAGE = 10;
export const SPECTER_ATTACK_RANGE = 4;
export const SPECTER_DETECTION_RANGE = 40;
export const SPECTER_COLORS = ['#ff4444', '#44ff44', '#4444ff', '#ffff44', '#ff44ff'];

// Define the different specter types with their respective textures
export const SPECTER_TYPES: SpecterType[] = [
  {
    id: 0, // Will be overridden with a random ID on creation
    name: "Echo Specter",
    color: "#ff4444",
    points: 50,
    texture: '/assets/textures/specter_sprite.png'
  },
  {
    id: 0,
    name: "Phantom Pat",
    color: "#e1a95f",
    points: 75,
    texture: '/assets/textures/GhostOfPat.png'
  },
  {
    id: 0,
    name: "Twin Specters",
    color: "#8844ff",
    points: 100,
    texture: '/assets/textures/TwinGhosts.png'
  },
  {
    id: 0,
    name: "Spectral Doctor",
    color: "#00ddff",
    points: 125,
    texture: '/assets/textures/doctor_sprite.png'
  },
  {
    id: 0,
    name: "Pharaoh Wraith",
    color: "#ffdd00",
    points: 150,
    texture: '/assets/textures/KingTut.png'
  }
];

// Weapon settings
export const AMMO_TYPES: AmmoType[] = [
  AmmoType.PhaseNet,
  AmmoType.TimeBubble,
  AmmoType.GravityWell
];

// Maximum ammo per type
export const MAX_AMMO = 5;
