import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { AmmoType } from '../types.js';
import { AMMO_TYPES } from '../constants.js';
import { audioManager } from '../audio/AudioManager.js';

class ShattershiftRifle {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private model: THREE.Group;
  private ammo: number[];
  private currentAmmoType: AmmoType;
  private recoilAnimation: {
    active: boolean;
    progress: number;
    duration: number;
  }
  private grapplingHookActive: boolean = false;
  private grapplingTarget: THREE.Vector3 | null = null;
  private homingEnabled: boolean = false;
  private homingTimer: number = 0;
  private enabled: boolean = true;

  constructor(scene: THREE.Scene, camera: THREE.PerspectiveCamera) {
    this.scene = scene;
    this.camera = camera;
    this.model = new THREE.Group();

    // Initialize ammo - each type starts with 5 rounds
    this.ammo = [5, 5, 5]; // Gravity, Time, Phase

    // Set default ammo type
    this.currentAmmoType = AmmoType.PhaseNet;

    // Set up recoil animation properties
    this.recoilAnimation = {
      active: false,
      progress: 0,
      duration: 0.2 // seconds
    };

    // Create rifle model
    this.createRifleModel();

    // Update rifle color to match the current ammo type
    this.updateRifleColor();

    // Add to scene
    this.scene.add(this.model);
  }

  update(delta: number): void {
    // Update weapon effects
    this.scene.traverse((object) => {
      if (object instanceof THREE.Group && object.userData.isWeaponEffect) {
        object.children.forEach((particle) => {
          if (particle.userData.velocity) {
            particle.position.add(particle.userData.velocity);
            particle.rotation.x += 0.01;
            particle.rotation.y += 0.01;

            // Scale down over time for fade effect
            particle.scale.multiplyScalar(0.99);

            // Remove tiny particles
            if (particle.scale.x < 0.01) {
              object.remove(particle);
            }
          }
        });
      }
    });

    // Position the rifle in front of the camera every frame
    this.positionRifle();

    // Update recoil animation
    if (this.recoilAnimation.active) {
      this.recoilAnimation.progress += delta;

      // Calculate recoil position
      const recoilAmount = Math.sin((this.recoilAnimation.progress / this.recoilAnimation.duration) * Math.PI) * 0.1;

      // Apply recoil
      this.model.position.z += recoilAmount;

      // End animation if complete
      if (this.recoilAnimation.progress >= this.recoilAnimation.duration) {
        this.recoilAnimation.active = false;
        this.recoilAnimation.progress = 0;
      }
    }
  }

  fireAmmo(): boolean {
    // If weapon is disabled, don't fire
    if (!this.enabled) {
      return false;
    }

    // Get ammo index
    const ammoIndex = AMMO_TYPES.indexOf(this.currentAmmoType);

    // For Phase Net (index 2), we have infinite ammo
    const isPhaseNet = this.currentAmmoType === AmmoType.PhaseNet;

    // Check if we have ammo (Phase Net always has ammo)
    if (!isPhaseNet && this.ammo[ammoIndex] <= 0) {
      return false;
    }

    // Reduce ammo (except for Phase Net)
    if (!isPhaseNet) {
      this.ammo[ammoIndex]--;
    }
    audioManager.playSound('shoot');
    // Start recoil animation
    this.recoilAnimation.active = true;
    this.recoilAnimation.progress = 0;

    // Determine effect type
    const effectType = this.currentAmmoType === AmmoType.GravityWell ? "gravity" :
                       this.currentAmmoType === AmmoType.TimeBubble ? "time" : "phase";

    // Create particle explosion effect
    const particleCount = 50;
    const particleGroup = new THREE.Group();
    const particleGeometry = new THREE.PlaneGeometry(0.05, 0.05); // Reduced size
    const radius = 0.2; // Adjust radius as needed

    // Load appropriate effect texture based on type
    const texturePath = effectType === "gravity" ? '/assets/textures/Pentagram.png' :
                       effectType === "time" ? '/assets/textures/time_particle.png' :
                       '/assets/textures/EyeOfHorus.png';

    const effectTexture = new THREE.TextureLoader().load(texturePath);

    for (let i = 0; i < particleCount; i++) {
      const particleMaterial = new THREE.MeshBasicMaterial({
        map: effectTexture,
        transparent: true,
        depthWrite: false,
        blending: THREE.AdditiveBlending
      });

      const particle = new THREE.Mesh(particleGeometry, particleMaterial);

      // Random position within sphere
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const r = Math.random() * radius;

      particle.position.x = r * Math.sin(phi) * Math.cos(theta);
      particle.position.y = r * Math.sin(phi) * Math.sin(theta);
      particle.position.z = r * Math.cos(phi);

      // Random rotation
      particle.rotation.x = Math.random() * Math.PI;
      particle.rotation.y = Math.random() * Math.PI;

      // Add velocity for animation
      particle.userData.velocity = new THREE.Vector3(
        (Math.random() - 0.5) * 0.1,
        (Math.random() - 0.5) * 0.1,
        (Math.random() - 0.5) * 0.1
      );

      particleGroup.add(particle);
    }

    particleGroup.userData.isWeaponEffect = true; // Tag for update function
    this.scene.add(particleGroup);

    return true;
  }

  setAmmoType(ammoType: AmmoType): void {
    this.currentAmmoType = ammoType;

    // Update rifle color based on ammo type
    this.updateRifleColor();
  }

  getCurrentAmmoType(): AmmoType {
    return this.currentAmmoType;
  }

  getAmmo(): number[] {
    return [...this.ammo];
  }

  refillAmmo(amount: number): void {
    // Get current ammo type index
    const ammoIndex = AMMO_TYPES.indexOf(this.currentAmmoType);

    // Add ammo up to the maximum
    const MAX_AMMO = 5;
    this.ammo[ammoIndex] = Math.min(this.ammo[ammoIndex] + amount, MAX_AMMO);
  }

  refillAmmoByType(ammoType: AmmoType, amount: number): void {
    // Find the index of the specified ammo type
    const ammoIndex = AMMO_TYPES.indexOf(ammoType);

    // If valid ammo type, refill it
    if (ammoIndex >= 0 && ammoIndex < this.ammo.length) {
      const MAX_AMMO = 5;
      this.ammo[ammoIndex] = Math.min(this.ammo[ammoIndex] + amount, MAX_AMMO);
    }
  }

  fireGrapplingHook(): void {
    if (this.grapplingHookActive) return;

    // Create raycaster from camera
    const raycaster = new THREE.Raycaster();
    const center = new THREE.Vector2(0, 0);
    raycaster.setFromCamera(center, this.camera);

    // Check for intersection with scene objects
    const intersects = raycaster.intersectObjects(this.scene.children, true);

    if (intersects.length > 0) {
      this.grapplingHookActive = true;
      this.grapplingTarget = intersects[0].point;

      // Create grappling hook effect
      const hookGeometry = new THREE.CylinderGeometry(0.02, 0.02, 1, 8);
      const hookMaterial = new THREE.MeshBasicMaterial({ color: 0x888888 });
      const hook = new THREE.Mesh(hookGeometry, hookMaterial);
      hook.userData.isGrapplingHook = true;
      this.scene.add(hook);

      // Play grappling sound
      audioManager.playSound('shoot');
    }
  }

  updateGrapplingHook(playerBody: CANNON.Body): void {
    if (!this.grapplingHookActive || !this.grapplingTarget) return;

    const currentPos = new THREE.Vector3(
      playerBody.position.x,
      playerBody.position.y,
      playerBody.position.z
    );

    const direction = this.grapplingTarget.clone().sub(currentPos);
    const distance = direction.length();

    if (distance < 1) {
      this.grapplingHookActive = false;
      this.grapplingTarget = null;

      // Find and remove grappling hook effect more safely
      const hookObjects: THREE.Object3D[] = [];
      if (this.scene) {
        this.scene.traverse((obj) => {
          if (obj.userData?.isGrapplingHook) {
            hookObjects.push(obj);
          }
        });

        // Remove found objects
        hookObjects.forEach(obj => {
          if (obj.parent) {
            obj.parent.remove(obj);
          }
        });
      }
      return;
    }

    // Apply grappling force
    direction.normalize().multiplyScalar(40);
    playerBody.velocity.set(direction.x, direction.y, direction.z);
  }

  // Also update the texture loading to handle errors gracefully
  private loadTexture(path: string): THREE.Texture {
    const texture = new THREE.TextureLoader().load(
      path,
      undefined,
      undefined,
      (error) => {
        console.warn('Error loading texture:', path, error);
        // Return a default colored texture on error
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 64;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, 64, 64);
        }
        texture.image = canvas;
        texture.needsUpdate = true;
      }
    );
    return texture;
  }

  enableHoming(duration: number = 30): void {
    // Ensure homing is enabled and timer is set
    this.homingEnabled = true;
    this.homingTimer = duration;

    // Log only when homing is enabled (not every frame)
    //console.log(`Homing missiles enabled for ${duration} seconds!`);

    // Ensure the model exists before trying to modify it
    if (!this.model) {
      console.warn("Rifle model not initialized when enabling homing");
      return;
    }

    // Dispatch custom event to notify game components directly
    try {
      const homingEvent = new CustomEvent('homingStatusChanged', {
        detail: { active: true, duration: duration }
      });
      document.dispatchEvent(homingEvent);
    } catch (e) {
      console.error("Failed to dispatch homing event:", e);
    }

    // Visual feedback - make it more noticeable
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.userData.isAmmoIndicator) {
        const material = child.material as THREE.MeshStandardMaterial;
        material.emissiveIntensity = 3;  // Increase emissive intensity
        material.emissive.set(0xff0000); // Set to red for homing
      }
    });

    // Play activation sound
    try {
      if (typeof audioManager !== 'undefined' && audioManager.playSound) {
        audioManager.playSound('powerup');
      }
    } catch (e) {
      console.warn("Could not play homing activation sound", e);
    }
  }

  updateHoming(delta: number): void {
    if (!this.homingEnabled) return;

    this.homingTimer -= delta;

    // Create a pulsing effect on the weapon when homing is active
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.userData.isAmmoIndicator) {
        const material = child.material as THREE.MeshStandardMaterial;
        // Make it pulse between 1.5 and 3 intensity
        const pulseFactor = 1.5 + Math.sin(Date.now() * 0.005) * 0.75;
        material.emissiveIntensity = pulseFactor;
      }
    });

    if (this.homingTimer <= 0) {
      this.homingEnabled = false;
      //console.log("Homing missiles deactivated");

      // Dispatch custom event to notify game components about deactivation
      try {
        const homingEvent = new CustomEvent('homingStatusChanged', {
          detail: { active: false, duration: 0 }
        });
        document.dispatchEvent(homingEvent);
      } catch (e) {
        console.error("Failed to dispatch homing deactivation event:", e);
      }

      // Reset visual feedback
      this.model.traverse((child) => {
        if (child instanceof THREE.Mesh && child.userData.isAmmoIndicator) {
          const material = child.material as THREE.MeshStandardMaterial;
          material.emissiveIntensity = 0.5;

          // Reset emissive color based on current ammo type
          let emissiveColor;
          switch (this.currentAmmoType) {
            case AmmoType.GravityWell:
              emissiveColor = 0x00ffff; // Cyan
              break;
            case AmmoType.TimeBubble:
              emissiveColor = 0xff00ff; // Magenta
              break;
            case AmmoType.PhaseNet:
              emissiveColor = 0xffff00; // Yellow
              break;
            default:
              emissiveColor = 0x00ffff;
          }
          material.emissive.set(emissiveColor);
        }
      });
    }
  }

  isHomingEnabled(): boolean {
    // Remove debug logging - this method is called every frame
    return this.homingEnabled;
  }

  /**
   * Enable or disable the weapon
   * @param enabled Whether the weapon is enabled
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;

    // Update visibility
    this.model.visible = enabled;
  }

  /**
   * Check if the weapon is enabled
   * @returns Whether the weapon is enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  reset(): void {
    // Reset ammo
    this.ammo = [5, 5, 5];

    // Reset ammo type
    this.currentAmmoType = AmmoType.PhaseNet;

    // Reset grappling hook
    this.grapplingHookActive = false;
    this.grapplingTarget = null;

    // Reset homing
    this.homingEnabled = false;
    this.homingTimer = 0;

    // Update rifle color
    this.updateRifleColor();
  }

  private createRifleModel(): void {
    // Create a simple low-poly rifle model

    // Main body
    const bodyGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.6);
    const bodyMaterial = new THREE.MeshStandardMaterial({
      color: 0x333333,
      metalness: 0.7,
      roughness: 0.2
    });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.z = -0.2;
    this.model.add(body);

    // Handle
    const handleGeometry = new THREE.BoxGeometry(0.08, 0.2, 0.1);
    const handleMaterial = new THREE.MeshStandardMaterial({
      color: 0x222222,
      roughness: 0.8
    });
    const handle = new THREE.Mesh(handleGeometry, handleMaterial);
    handle.position.set(0, -0.15, -0.15);
    handle.rotation.x = Math.PI * 0.1;
    this.model.add(handle);

    // Barrel
    const barrelGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.6, 8);
    const barrelMaterial = new THREE.MeshStandardMaterial({
      color: 0x444444,
      metalness: 0.8,
      roughness: 0.2
    });
    const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
    barrel.rotation.x = Math.PI * 0.5;
    barrel.position.set(0, 0, -0.6);
    this.model.add(barrel);

    // Ammo chamber (will be colored based on current ammo)
    const chamberGeometry = new THREE.SphereGeometry(0.07, 8, 8);
    const chamberMaterial = new THREE.MeshStandardMaterial({
      color: 0xffff00, // Default color (phase)
      emissive: 0xffff00,
      emissiveIntensity: 0.5,
      transparent: true,
      opacity: 0.7
    });
    const chamber = new THREE.Mesh(chamberGeometry, chamberMaterial);
    chamber.position.set(0, 0.08, -0.3);
    chamber.userData.isAmmoIndicator = true; // Tag for color updates
    this.model.add(chamber);

    // Scope
    const scopeGeometry = new THREE.CylinderGeometry(0.02, 0.03, 0.1, 8);
    const scopeMaterial = new THREE.MeshStandardMaterial({
      color: 0x222222,
      metalness: 0.5,
      roughness: 0.2
    });
    const scope = new THREE.Mesh(scopeGeometry, scopeMaterial);
    scope.rotation.x = Math.PI * 0.5;
    scope.position.set(0, 0.06, -0.1);
    this.model.add(scope);

    // Scale and position the whole model
    this.model.scale.set(0.5, 0.5, 0.5);
  }

  private positionRifle(): void {
    // Position rifle in front of camera
    const offset = new THREE.Vector3(0.3, -0.3, -0.7);

    // Get camera direction
    const cameraDirection = new THREE.Vector3();
    this.camera.getWorldDirection(cameraDirection);

    // Create rotation matrix from camera direction
    const rotationMatrix = new THREE.Matrix4();
    rotationMatrix.lookAt(
      new THREE.Vector3(0, 0, 0),
      cameraDirection,
      new THREE.Vector3(0, 1, 0)
    );

    // Apply rotation to offset
    offset.applyMatrix4(rotationMatrix);

    // Set position
    this.model.position.copy(this.camera.position).add(offset);

    // Set rotation to match camera
    this.model.quaternion.copy(this.camera.quaternion);

    // Add a slight angle for better visibility
    this.model.rotation.x -= 0.1;
    this.model.rotation.y += 0.2;
  }

  private updateRifleColor(): void {
    // Update the color of the ammo chamber based on current ammo type
    let color: number;

    switch (this.currentAmmoType) {
      case AmmoType.GravityWell:
        color = 0x00ffff; // Cyan for gravity
        break;
      case AmmoType.TimeBubble:
        color = 0xff00ff; // Magenta for time
        break;
      case AmmoType.PhaseNet:
        color = 0xffff00; // Yellow for phase
        break;
      default:
        color = 0x00ffff; // Default color
    }

    // Find ammo indicator and update its color
    this.model.traverse((child) => {
      if (child instanceof THREE.Mesh && child.userData.isAmmoIndicator) {
        const material = child.material as THREE.MeshStandardMaterial;
        material.color.set(color);
        material.emissive.set(color);
      }
    });
  }

  /**
   * Check if the grappling hook is currently active
   * @returns True if the grappling hook is active, false otherwise
   */
  isGrapplingHookActive(): boolean {
    return this.grapplingHookActive;
  }
}

export default ShattershiftRifle;