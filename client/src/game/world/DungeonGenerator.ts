import * as THREE from 'three';
import * as CANN<PERSON> from 'cannon-es';
import { SeededRandom } from '../utils/SeededRandom.js';
import { DungeonRoom } from './DungeonRoom.js';
import Specter from '../entities/Specter.js';
import { DungeonBoss } from '../entities/DungeonBoss.js';
import { DungeonLoot } from '../entities/DungeonLoot.js';
import { PetSpecter } from '../entities/PetSpecter.js';
import { audioManager as AudioManager } from '../audio/AudioManager.js';
import { SPECTER_TYPES } from '../constants.js';
import { SpecterType } from '../types.js';

// Room types for dungeon generation
export enum DungeonRoomType {
  ENTRANCE = 'entrance',
  CORRIDOR = 'corridor',
  REGULAR = 'regular',
  BOSS = 'boss',
  EXIT = 'exit'
}

// Dungeon difficulty levels
export enum DungeonDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  NIGHTMARE = 'nightmare'
}

// Structure to track dungeon layout
interface DungeonLayout {
  rooms: Map<string, DungeonRoom>;
  currentRoom: string; // Key of current room
  bossRoom: string; // Key of boss room
  exitRoom: string; // Key of exit room
  entranceRoom: string; // Key of entrance room
  visitedRooms: Set<string>; // Rooms the player has visited
  clearedRooms: Set<string>; // Rooms that have been cleared of enemies
}

/**
 * DungeonGenerator class for creating procedural dungeons
 */
export class DungeonGenerator {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private layout: DungeonLayout;
  private random: SeededRandom;
  private difficulty: DungeonDifficulty;
  private dungeonLevel: number;
  private playerPet: PetSpecter;
  private roomSize: number = 20; // Size of each room
  private roomHeight: number = 6; // Height of rooms
  private maxRooms: number = 10; // Maximum number of rooms in a dungeon
  private enemies: Specter[] = [];
  private bosses: DungeonBoss[] = [];
  private loots: DungeonLoot[] = [];
  private isActive: boolean = false;
  private physicsMaterial: CANNON.Material;

  // Callbacks for game events
  public onEnemyDefeated: (enemyData: any) => void = () => {};
  public onLootCollected: (lootData: any) => void = () => {};
  public onRoomCleared: (roomKey: string) => void = () => {};
  public onBossDefeated: (bossData: any) => void = () => {};
  public onDungeonCompleted: () => void = () => {};
  public onDungeonFailed: () => void = () => {};

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    difficulty: DungeonDifficulty,
    dungeonLevel: number,
    playerPet: PetSpecter
  ) {
    this.scene = scene;
    this.world = world;
    this.difficulty = difficulty;
    this.dungeonLevel = dungeonLevel;
    this.playerPet = playerPet;

    // Create physics material for enemies
    this.physicsMaterial = new CANNON.Material('specterMaterial');

    // Initialize with a seed based on difficulty and level
    // We're removing the time-based randomness to ensure consistent generation for debugging
    const seed = dungeonLevel * 1000 +
                Object.values(DungeonDifficulty).indexOf(difficulty) * 100;
    this.random = new SeededRandom(seed);
    console.log(`Generated dungeon with seed: ${seed}`);

    // Initialize empty layout
    this.layout = {
      rooms: new Map<string, DungeonRoom>(),
      currentRoom: '0,0',
      bossRoom: '',
      exitRoom: '',
      entranceRoom: '0,0',
      visitedRooms: new Set<string>(),
      clearedRooms: new Set<string>()
    };

    // Set a variable room count based on dungeon level
    // This ensures we have enough rooms to create a proper DOOM-like dungeon layout
    this.maxRooms = 10 + Math.min(10, this.dungeonLevel * 2); // More rooms for higher levels
  }

  /**
   * Generate the dungeon layout
   */
  generateDungeon(): void {
    console.log('Starting dungeon generation with max rooms:', this.maxRooms);

    // Clear any existing dungeon
    this.clearDungeon();

    // Set active flag
    this.isActive = true;

    // Create entrance room at 0,0
    this.createRoom(0, 0, DungeonRoomType.ENTRANCE);
    this.layout.entranceRoom = '0,0';
    this.layout.currentRoom = '0,0';
    this.layout.visitedRooms.add('0,0');
    console.log('Created entrance room at 0,0');

    // Generate dungeon layout using a branching path algorithm
    let roomCount = 1; // Start with entrance room
    const maxAttempts = 200; // Prevent infinite loops
    let attemptCount = 0;

    // Keep track of all possible expansion points
    const expansionPoints: {x: number, y: number}[] = [{x: 0, y: 0}];
    console.log('Initial expansion point: 0,0');

    // Create a more DOOM-like layout with branching corridors and varied room types
    while (roomCount < this.maxRooms && expansionPoints.length > 0 && attemptCount < maxAttempts) {
      attemptCount++;

      // Choose a random expansion point, with preference for points further from entrance
      // This creates more interesting layouts with longer paths
      let pointIndex;
      if (this.random.between(0, 1) < 0.7) {
        // 70% chance to pick a point further from entrance
        let furthestDistance = 0;
        let furthestIndex = 0;

        // Find the furthest points
        expansionPoints.forEach((point, index) => {
          const distance = Math.abs(point.x) + Math.abs(point.y); // Manhattan distance
          if (distance > furthestDistance) {
            furthestDistance = distance;
            furthestIndex = index;
          }
        });

        // Pick one of the furthest points (within 1 distance unit)
        const furthestPoints = expansionPoints.filter(point => {
          const distance = Math.abs(point.x) + Math.abs(point.y);
          return distance >= furthestDistance - 1;
        });

        const randomFurthestIndex = this.random.intBetween(0, furthestPoints.length - 1);
        pointIndex = expansionPoints.indexOf(furthestPoints[randomFurthestIndex]);
      } else {
        // 30% chance to pick a random point
        pointIndex = this.random.intBetween(0, expansionPoints.length - 1);
      }

      const currentPoint = expansionPoints[pointIndex];
      const currentX = currentPoint.x;
      const currentY = currentPoint.y;
      console.log(`Attempt ${attemptCount}: Expanding from point (${currentX},${currentY})`);

      // Try to expand in a random direction
      // For DOOM-like layouts, we want to encourage longer corridors in some directions
      const directions = [
        {dx: 0, dy: 1, weight: 1.0},  // North
        {dx: 1, dy: 0, weight: 1.0},  // East
        {dx: 0, dy: -1, weight: 1.0}, // South
        {dx: -1, dy: 0, weight: 1.0}  // West
      ];

      // Add diagonal directions with lower weights for more varied layouts
      if (this.random.between(0, 1) < 0.3) { // 30% chance to consider diagonals
        directions.push({dx: 1, dy: 1, weight: 0.5});   // Northeast
        directions.push({dx: 1, dy: -1, weight: 0.5});  // Southeast
        directions.push({dx: -1, dy: -1, weight: 0.5}); // Southwest
        directions.push({dx: -1, dy: 1, weight: 0.5});  // Northwest
      }

      // Shuffle directions for more randomness
      this.random.shuffle(directions);
      console.log('Shuffled directions:', directions.map(d => `(${d.dx},${d.dy})`).join(', '));

      let expanded = false;

      // Try each direction until we find a valid expansion
      for (const dir of directions) {
        // Skip some directions based on their weight
        if (this.random.between(0, 1) > dir.weight) {
          continue;
        }

        const newX = currentX + dir.dx;
        const newY = currentY + dir.dy;
        const roomKey = `${newX},${newY}`;
        console.log(`Trying direction (${dir.dx},${dir.dy}) to create room at (${newX},${newY})`);

        // Check if this position already has a room
        if (!this.layout.rooms.has(roomKey)) {
          // Determine room type
          let roomType: DungeonRoomType;

          // Special rooms are placed based on distance from entrance
          const distanceFromEntrance = Math.abs(newX) + Math.abs(newY); // Manhattan distance

          // Place boss room at a good distance from entrance
          const targetBossDistance = Math.floor(this.maxRooms * 0.6);
          const isGoodBossLocation = distanceFromEntrance >= targetBossDistance && !this.layout.bossRoom;

          // Place exit room near boss room
          const isBossNeighbor = this.layout.bossRoom && (
            Math.abs(newX - parseInt(this.layout.bossRoom.split(',')[0])) +
            Math.abs(newY - parseInt(this.layout.bossRoom.split(',')[1]))
          ) === 1;

          if (isGoodBossLocation && roomCount >= this.maxRooms * 0.7) {
            // Place boss room at a good distance when we've created enough rooms
            roomType = DungeonRoomType.BOSS;
            this.layout.bossRoom = roomKey;
            console.log(`Creating BOSS room at (${newX},${newY}) at distance ${distanceFromEntrance}`);
          } else if (isBossNeighbor && !this.layout.exitRoom) {
            // Place exit room adjacent to boss room
            roomType = DungeonRoomType.EXIT;
            this.layout.exitRoom = roomKey;
            console.log(`Creating EXIT room at (${newX},${newY}) next to boss room`);
          } else {
            // All non-special rooms are regular rooms
            roomType = DungeonRoomType.REGULAR;
            console.log(`Creating REGULAR room at (${newX},${newY}) at distance ${distanceFromEntrance}`);
          }

          // Create the room
          this.createRoom(newX, newY, roomType);

          // Create corridor between current room and new room
          this.createCorridor(currentX, currentY, newX, newY);

          // Add the new point as a potential expansion point
          expansionPoints.push({x: newX, y: newY});
          console.log(`Added new expansion point (${newX},${newY})`);

          // For DOOM-like layouts, we want to create some dead ends
          // but also allow for more interconnected areas
          const deadEndChance = roomType === DungeonRoomType.BOSS ? 0.9 : // High chance to dead-end after boss
                               0.3; // Lower chance for regular rooms

          // Remove current point with probability based on room type to create dead ends
          if (this.random.between(0, 1) < deadEndChance) {
            expansionPoints.splice(pointIndex, 1);
            console.log(`Removed expansion point (${currentX},${currentY}) to create dead end (${roomType} room)`);
          }

          roomCount++;
          expanded = true;
          console.log(`Room count: ${roomCount}/${this.maxRooms}`);

          // For DOOM-like layouts, sometimes we want to create loops
          // by connecting to existing rooms (except for boss and exit rooms)
          if (roomCount > 5 && this.random.between(0, 1) < 0.2) { // 20% chance to create a loop
            this.tryCreateLoop(newX, newY);
          }

          break;
        } else {
          console.log(`Room already exists at (${newX},${newY}), trying another direction`);
        }
      }

      // If we couldn't expand from this point, remove it
      if (!expanded) {
        expansionPoints.splice(pointIndex, 1);
        console.log(`Couldn't expand from (${currentX},${currentY}), removing point. Remaining points: ${expansionPoints.length}`);
      }
    }

    console.log(`Dungeon generation complete. Created ${roomCount} rooms out of ${this.maxRooms} max rooms.`);
    console.log(`Expansion points remaining: ${expansionPoints.length}`);
    console.log(`Attempts used: ${attemptCount}/${maxAttempts}`);

    // Log all room positions for debugging
    console.log('Room positions:');
    this.layout.rooms.forEach((room, key) => {
      console.log(`- ${key}: ${room.getRoomType()}`);
    });

    // If we couldn't place all rooms, ensure we at least have a boss and exit
    if (!this.layout.bossRoom || !this.layout.exitRoom) {
      // Find the furthest room from entrance
      let furthestRoom = '0,0';
      let maxDistance = 0;

      this.layout.rooms.forEach((room, key) => {
        const [x, y] = key.split(',').map(Number);
        const distance = Math.abs(x) + Math.abs(y); // Manhattan distance

        if (distance > maxDistance && key !== this.layout.entranceRoom) {
          maxDistance = distance;
          furthestRoom = key;
        }
      });

      // If no boss room was created, convert the furthest room to boss
      if (!this.layout.bossRoom) {
        const [bossX, bossY] = furthestRoom.split(',').map(Number);
        this.convertRoomType(bossX, bossY, DungeonRoomType.BOSS);
        this.layout.bossRoom = furthestRoom;

        // Create exit room adjacent to boss room
        const exitDirections = [
          [0, 1], [1, 0], [0, -1], [-1, 0] // N, E, S, W
        ];

        for (const [dx, dy] of exitDirections) {
          const exitX = bossX + dx;
          const exitY = bossY + dy;
          const exitKey = `${exitX},${exitY}`;

          if (!this.layout.rooms.has(exitKey)) {
            this.createRoom(exitX, exitY, DungeonRoomType.EXIT);
            this.createCorridor(bossX, bossY, exitX, exitY);
            this.layout.exitRoom = exitKey;
            break;
          }
        }
      }
    }

    // Populate rooms with enemies, loot, etc.
    this.populateRooms();

    console.log(`Generated dungeon with ${this.layout.rooms.size} rooms`);
  }

  /**
   * Create a single dungeon room
   */
  private createRoom(x: number, y: number, type: DungeonRoomType): void {
    const roomKey = `${x},${y}`;

    // Calculate world position
    const worldX = x * (this.roomSize + 5); // Add spacing between rooms
    const worldZ = y * (this.roomSize + 5);

    // Create room
    const room = new DungeonRoom(
      this.scene,
      this.world,
      new THREE.Vector3(worldX, 0, worldZ),
      this.roomSize,
      this.roomHeight,
      type,
      this.difficulty,
      this.dungeonLevel,
      this.random.getSeed() + x * 100 + y // Unique seed for each room
    );

    // Add to layout
    this.layout.rooms.set(roomKey, room);
  }

  /**
   * Create a corridor between two rooms
   */
  private createCorridor(x1: number, y1: number, x2: number, y2: number): void {
    // Calculate world positions
    const worldX1 = x1 * (this.roomSize + 5);
    const worldZ1 = y1 * (this.roomSize + 5);
    const worldX2 = x2 * (this.roomSize + 5);
    const worldZ2 = y2 * (this.roomSize + 5);

    // Create DOOM-like corridor (wider and more detailed)
    const corridorWidth = 6; // Wider corridor for better movement

    // Determine if corridor is horizontal or vertical
    const isHorizontal = y1 === y2;
    const length = isHorizontal ?
      Math.abs(worldX2 - worldX1) :
      Math.abs(worldZ2 - worldZ1);

    // Load textures for corridor
    const textureLoader = new THREE.TextureLoader();

    // Use existing SVG textures from the assets folder for floor, walls, and ceiling
    const floorTexture = textureLoader.load('assets/textures/level/floor1.svg');
    floorTexture.wrapS = THREE.RepeatWrapping;
    floorTexture.wrapT = THREE.RepeatWrapping;
    floorTexture.repeat.set(Math.ceil(length/4), Math.ceil(corridorWidth/4));

    const wallTexture = textureLoader.load('assets/textures/level/wall1.svg');
    wallTexture.wrapS = THREE.RepeatWrapping;
    wallTexture.wrapT = THREE.RepeatWrapping;
    wallTexture.repeat.set(Math.ceil(length/4), Math.ceil(this.roomHeight/4));

    const ceilingTexture = textureLoader.load('assets/textures/level/floor2.svg');
    ceilingTexture.wrapS = THREE.RepeatWrapping;
    ceilingTexture.wrapT = THREE.RepeatWrapping;
    ceilingTexture.repeat.set(Math.ceil(length/4), Math.ceil(corridorWidth/4));

    // Create materials
    const floorMaterial = new THREE.MeshStandardMaterial({
      color: 0x777777,
      roughness: 0.7,
      metalness: 0.2,
      map: floorTexture
    });

    const wallMaterial = new THREE.MeshStandardMaterial({
      color: 0xcccccc, // Brighter color to match room walls
      roughness: 0.5,  // Lower roughness for better light reflection
      metalness: 0.3,  // Slightly higher metalness
      map: wallTexture,
      side: THREE.DoubleSide, // Make sure walls are visible from both sides
      emissive: 0x222222, // Slight emissive to ensure visibility even in darker areas
      emissiveIntensity: 0.1
    });

    const ceilingMaterial = new THREE.MeshStandardMaterial({
      color: 0x666666,
      roughness: 0.7,
      metalness: 0.2,
      map: ceilingTexture
    });

    // Create corridor components (floor, walls, ceiling) instead of a single box
    // This creates a hollow corridor that players can walk through

    // Calculate positions and dimensions
    let startX, startZ, endX, endZ;
    if (isHorizontal) {
      // Horizontal corridor (East-West)
      startX = Math.min(worldX1, worldX2);
      endX = Math.max(worldX1, worldX2);
      startZ = worldZ1 - corridorWidth/2;
      endZ = worldZ1 + corridorWidth/2;
    } else {
      // Vertical corridor (North-South)
      startX = worldX1 - corridorWidth/2;
      endX = worldX1 + corridorWidth/2;
      startZ = Math.min(worldZ1, worldZ2);
      endZ = Math.max(worldZ1, worldZ2);
    }

    // Create floor
    const floorGeometry = new THREE.PlaneGeometry(
      isHorizontal ? length : corridorWidth,
      isHorizontal ? corridorWidth : length
    );
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2; // Rotate to be horizontal

    if (isHorizontal) {
      floor.position.set(
        (startX + endX) / 2,
        0.01, // Slightly above ground to prevent z-fighting
        worldZ1
      );
    } else {
      floor.position.set(
        worldX1,
        0.01,
        (startZ + endZ) / 2
      );
    }

    // Add floor to scene
    this.scene.add(floor);

    // Create ceiling
    const ceilingGeometry = new THREE.PlaneGeometry(
      isHorizontal ? length : corridorWidth,
      isHorizontal ? corridorWidth : length
    );
    const ceiling = new THREE.Mesh(ceilingGeometry, ceilingMaterial);
    ceiling.rotation.x = Math.PI / 2; // Rotate to be horizontal but facing down

    if (isHorizontal) {
      ceiling.position.set(
        (startX + endX) / 2,
        this.roomHeight - 0.01,
        worldZ1
      );
    } else {
      ceiling.position.set(
        worldX1,
        this.roomHeight - 0.01,
        (startZ + endZ) / 2
      );
    }

    // Add ceiling to scene
    this.scene.add(ceiling);

    // Create walls (only two walls are needed for each corridor)
    if (isHorizontal) {
      // North wall
      const northWallGeometry = new THREE.PlaneGeometry(length, this.roomHeight);
      const northWall = new THREE.Mesh(northWallGeometry, wallMaterial);
      northWall.position.set(
        (startX + endX) / 2,
        this.roomHeight / 2,
        startZ
      );
      northWall.rotation.y = Math.PI; // Face inside the corridor
      this.scene.add(northWall);

      // South wall
      const southWallGeometry = new THREE.PlaneGeometry(length, this.roomHeight);
      const southWall = new THREE.Mesh(southWallGeometry, wallMaterial);
      southWall.position.set(
        (startX + endX) / 2,
        this.roomHeight / 2,
        endZ
      );
      this.scene.add(southWall);

      // Create doorways at the ends of the corridor
      // East doorway (connecting to the room at x2,y2)
      const eastDoorwayGeometry = new THREE.PlaneGeometry(corridorWidth, this.roomHeight);
      const eastDoorway = new THREE.Mesh(eastDoorwayGeometry, wallMaterial);
      eastDoorway.position.set(
        endX,
        this.roomHeight / 2,
        worldZ1
      );
      eastDoorway.rotation.y = -Math.PI / 2; // Face inside the corridor
      this.scene.add(eastDoorway);

      // West doorway (connecting to the room at x1,y1)
      const westDoorwayGeometry = new THREE.PlaneGeometry(corridorWidth, this.roomHeight);
      const westDoorway = new THREE.Mesh(westDoorwayGeometry, wallMaterial);
      westDoorway.position.set(
        startX,
        this.roomHeight / 2,
        worldZ1
      );
      westDoorway.rotation.y = Math.PI / 2; // Face inside the corridor
      this.scene.add(westDoorway);
    } else {
      // East wall
      const eastWallGeometry = new THREE.PlaneGeometry(length, this.roomHeight);
      const eastWall = new THREE.Mesh(eastWallGeometry, wallMaterial);
      eastWall.position.set(
        startX,
        this.roomHeight / 2,
        (startZ + endZ) / 2
      );
      eastWall.rotation.y = Math.PI / 2; // Face inside the corridor
      this.scene.add(eastWall);

      // West wall
      const westWallGeometry = new THREE.PlaneGeometry(length, this.roomHeight);
      const westWall = new THREE.Mesh(westWallGeometry, wallMaterial);
      westWall.position.set(
        endX,
        this.roomHeight / 2,
        (startZ + endZ) / 2
      );
      westWall.rotation.y = -Math.PI / 2; // Face inside the corridor
      this.scene.add(westWall);

      // Create doorways at the ends of the corridor
      // North doorway (connecting to the room at x2,y2)
      const northDoorwayGeometry = new THREE.PlaneGeometry(corridorWidth, this.roomHeight);
      const northDoorway = new THREE.Mesh(northDoorwayGeometry, wallMaterial);
      northDoorway.position.set(
        worldX1,
        this.roomHeight / 2,
        endZ
      );
      northDoorway.rotation.y = 0; // Face inside the corridor
      this.scene.add(northDoorway);

      // South doorway (connecting to the room at x1,y1)
      const southDoorwayGeometry = new THREE.PlaneGeometry(corridorWidth, this.roomHeight);
      const southDoorway = new THREE.Mesh(southDoorwayGeometry, wallMaterial);
      southDoorway.position.set(
        worldX1,
        this.roomHeight / 2,
        startZ
      );
      southDoorway.rotation.y = Math.PI; // Face inside the corridor
      this.scene.add(southDoorway);
    }

    // Add enhanced lighting to the corridor
    // Increase number of lights and their intensity for better visibility
    const numLights = Math.max(2, Math.floor(length / 8));
    for (let i = 0; i < numLights; i++) {
      const lightPos = i / (numLights - 1 || 1); // 0 to 1
      const light = new THREE.PointLight(0xffffcc, 1.0, corridorWidth * 3);

      if (isHorizontal) {
        light.position.set(
          startX + lightPos * length,
          this.roomHeight - 1,
          worldZ1
        );
      } else {
        light.position.set(
          worldX1,
          this.roomHeight - 1,
          startZ + lightPos * length
        );
      }

      this.scene.add(light);
    }

    // Add wall lights to ensure corridor walls are visible
    if (isHorizontal) {
      // Add lights along the north and south walls
      const wallLight1 = new THREE.PointLight(0xffffcc, 0.7, corridorWidth * 2);
      wallLight1.position.set(
        (startX + endX) / 2,
        this.roomHeight / 2,
        startZ + 0.5 // North wall
      );
      this.scene.add(wallLight1);

      const wallLight2 = new THREE.PointLight(0xffffcc, 0.7, corridorWidth * 2);
      wallLight2.position.set(
        (startX + endX) / 2,
        this.roomHeight / 2,
        endZ - 0.5 // South wall
      );
      this.scene.add(wallLight2);
    } else {
      // Add lights along the east and west walls
      const wallLight1 = new THREE.PointLight(0xffffcc, 0.7, corridorWidth * 2);
      wallLight1.position.set(
        startX + 0.5, // East wall
        this.roomHeight / 2,
        (startZ + endZ) / 2
      );
      this.scene.add(wallLight1);

      const wallLight2 = new THREE.PointLight(0xffffcc, 0.7, corridorWidth * 2);
      wallLight2.position.set(
        endX - 0.5, // West wall
        this.roomHeight / 2,
        (startZ + endZ) / 2
      );
      this.scene.add(wallLight2);
    }

    console.log(`Created DOOM-like corridor from (${x1},${y1}) to (${x2},${y2})`);

    // Add physics for corridor walls (invisible collision boxes)
    // We only need collision for the walls, not floor/ceiling
    if (isHorizontal) {
      // North wall collision
      const northWallShape = new CANNON.Box(new CANNON.Vec3(
        length / 2,
        this.roomHeight / 2,
        0.5 // Thin collision box
      ));
      const northWallBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          (startX + endX) / 2,
          this.roomHeight / 2,
          startZ
        ),
        shape: northWallShape
      });
      this.world.addBody(northWallBody);

      // South wall collision
      const southWallShape = new CANNON.Box(new CANNON.Vec3(
        length / 2,
        this.roomHeight / 2,
        0.5
      ));
      const southWallBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          (startX + endX) / 2,
          this.roomHeight / 2,
          endZ
        ),
        shape: southWallShape
      });
      this.world.addBody(southWallBody);

      // East doorway collision
      const eastDoorwayShape = new CANNON.Box(new CANNON.Vec3(
        0.5, // Thin collision box
        this.roomHeight / 2,
        corridorWidth / 2
      ));
      const eastDoorwayBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          endX,
          this.roomHeight / 2,
          worldZ1
        ),
        shape: eastDoorwayShape
      });
      this.world.addBody(eastDoorwayBody);

      // West doorway collision
      const westDoorwayShape = new CANNON.Box(new CANNON.Vec3(
        0.5,
        this.roomHeight / 2,
        corridorWidth / 2
      ));
      const westDoorwayBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          startX,
          this.roomHeight / 2,
          worldZ1
        ),
        shape: westDoorwayShape
      });
      this.world.addBody(westDoorwayBody);
    } else {
      // East wall collision
      const eastWallShape = new CANNON.Box(new CANNON.Vec3(
        0.5,
        this.roomHeight / 2,
        length / 2
      ));
      const eastWallBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          startX,
          this.roomHeight / 2,
          (startZ + endZ) / 2
        ),
        shape: eastWallShape
      });
      this.world.addBody(eastWallBody);

      // West wall collision
      const westWallShape = new CANNON.Box(new CANNON.Vec3(
        0.5,
        this.roomHeight / 2,
        length / 2
      ));
      const westWallBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          endX,
          this.roomHeight / 2,
          (startZ + endZ) / 2
        ),
        shape: westWallShape
      });
      this.world.addBody(westWallBody);

      // North doorway collision
      const northDoorwayShape = new CANNON.Box(new CANNON.Vec3(
        corridorWidth / 2,
        this.roomHeight / 2,
        0.5 // Thin collision box
      ));
      const northDoorwayBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          worldX1,
          this.roomHeight / 2,
          endZ
        ),
        shape: northDoorwayShape
      });
      this.world.addBody(northDoorwayBody);

      // South doorway collision
      const southDoorwayShape = new CANNON.Box(new CANNON.Vec3(
        corridorWidth / 2,
        this.roomHeight / 2,
        0.5
      ));
      const southDoorwayBody = new CANNON.Body({
        mass: 0,
        position: new CANNON.Vec3(
          worldX1,
          this.roomHeight / 2,
          startZ
        ),
        shape: southDoorwayShape
      });
      this.world.addBody(southDoorwayBody);
    }
  }

  /**
   * Convert a room to a different type
   */
  private convertRoomType(x: number, y: number, newType: DungeonRoomType): void {
    const roomKey = `${x},${y}`;
    const room = this.layout.rooms.get(roomKey);

    if (room) {
      room.setRoomType(newType);
    }
  }

  /**
   * Try to create a loop by connecting to an existing room
   * This creates more interconnected DOOM-like layouts
   */
  private tryCreateLoop(x: number, y: number): void {
    // Look for nearby rooms that aren't directly connected
    const directions = [
      {dx: 0, dy: 2},   // Two rooms north
      {dx: 2, dy: 0},   // Two rooms east
      {dx: 0, dy: -2},  // Two rooms south
      {dx: -2, dy: 0},  // Two rooms west
      {dx: 1, dy: 1},   // Northeast
      {dx: 1, dy: -1},  // Southeast
      {dx: -1, dy: -1}, // Southwest
      {dx: -1, dy: 1}   // Northwest
    ];

    // Shuffle directions for randomness
    this.random.shuffle(directions);

    // Try each direction
    for (const dir of directions) {
      const targetX = x + dir.dx;
      const targetY = y + dir.dy;
      const targetKey = `${targetX},${targetY}`;

      // Check if target room exists and is not a boss or exit room
      const targetRoom = this.layout.rooms.get(targetKey);
      if (targetRoom &&
          targetRoom.getRoomType() !== DungeonRoomType.BOSS &&
          targetRoom.getRoomType() !== DungeonRoomType.EXIT) {

        // Create a corridor to connect the rooms
        this.createCorridor(x, y, targetX, targetY);
        console.log(`Created loop connection from (${x},${y}) to (${targetX},${targetY})`);

        // Only create one loop connection per attempt
        return;
      }
    }
  }

  /**
   * Populate rooms with enemies, loot, etc.
   */
  private populateRooms(): void {
    // Maximum number of enemies allowed in the dungeon at any time
    const MAX_ENEMIES = 6;
    let totalEnemies = 0;

    // First populate boss room as it's the most important
    this.layout.rooms.forEach((room, key) => {
      if (room.getRoomType() === DungeonRoomType.BOSS) {
        this.populateBossRoom(room);
      }
    });

    // Then populate regular rooms with enemies, keeping track of enemy count
    // We'll distribute enemies across regular rooms until we reach the maximum
    const regularRooms: DungeonRoom[] = [];

    // Collect all regular rooms
    this.layout.rooms.forEach((room, key) => {
      const roomType = room.getRoomType();
      if (roomType === DungeonRoomType.REGULAR) {
        regularRooms.push(room);
      }
    });

    // Shuffle the rooms to randomize enemy placement
    this.random.shuffle(regularRooms);

    // Distribute enemies across regular rooms
    for (const room of regularRooms) {
      // Check if we've reached the enemy limit
      if (totalEnemies >= MAX_ENEMIES) {
        console.log(`Reached maximum enemy count (${MAX_ENEMIES}), not spawning more enemies`);
        break;
      }

      // Calculate how many more enemies we can spawn
      const remainingEnemySlots = MAX_ENEMIES - totalEnemies;
      if (remainingEnemySlots > 0) {
        const enemiesAdded = this.populateRegularRoom(room, remainingEnemySlots);
        totalEnemies += enemiesAdded;
      }
    }

    console.log(`Populated dungeon with ${totalEnemies} total enemies (max: ${MAX_ENEMIES})`);
  }

  /**
   * Populate a regular room with enemies
   * @param room The room to populate
   * @param maxEnemies Maximum number of enemies to spawn
   * @returns Number of enemies actually spawned
   */
  private populateRegularRoom(room: DungeonRoom, maxEnemies: number = 6): number {
    const roomPosition = room.getPosition();
    const roomSize = room.getSize();

    // Number of enemies based on difficulty and dungeon level
    const baseEnemyCount = 2;
    const difficultyMultiplier = {
      [DungeonDifficulty.EASY]: 1,
      [DungeonDifficulty.MEDIUM]: 1.5,
      [DungeonDifficulty.HARD]: 2,
      [DungeonDifficulty.NIGHTMARE]: 3
    };

    // Calculate desired enemy count based on difficulty and level
    let desiredEnemyCount = Math.floor(
      baseEnemyCount * difficultyMultiplier[this.difficulty] *
      (1 + this.dungeonLevel * 0.1)
    );

    // Limit to the maximum allowed
    const enemyCount = Math.min(desiredEnemyCount, maxEnemies);
    console.log(`Populating regular room with ${enemyCount} enemies (max: ${maxEnemies})`);

    // Spawn enemies
    for (let i = 0; i < enemyCount; i++) {
      // Random position within room
      const posX = roomPosition.x + this.random.between(-roomSize/3, roomSize/3);
      const posZ = roomPosition.z + this.random.between(-roomSize/3, roomSize/3);

      // Select a random specter type from the available types
      const specterTypeIndex = this.random.intBetween(0, SPECTER_TYPES.length - 1);
      const specterType = SPECTER_TYPES[specterTypeIndex];

      // Create a copy of the specter type with a unique ID
      const enemyType: SpecterType = {
        ...specterType,
        id: this.random.getSeed() + i // Ensure unique ID
      };

      // Create enemy using the standard Specter class
      const enemy = new Specter(
        this.scene,
        this.world,
        new THREE.Vector3(posX, 1, posZ),
        this.physicsMaterial,
        enemyType
      );

      // Store reference to the enemy
      this.enemies.push(enemy);

      // Add user data to the mesh for identification
      enemy.getMesh().userData = {
        isEnemy: true,
        specterReference: enemy,
        difficulty: this.difficulty,
        dungeonLevel: this.dungeonLevel,
        points: enemyType.points * (1 + this.dungeonLevel * 0.2)
      };
    }

    // Occasionally add some loot to regular rooms (25% chance)
    if (this.random.between(0, 1) < 0.25) {
      // Random position within room
      const posX = roomPosition.x + this.random.between(-roomSize/4, roomSize/4);
      const posZ = roomPosition.z + this.random.between(-roomSize/4, roomSize/4);

      // Create loot
      const loot = new DungeonLoot(
        this.scene,
        this.world,
        new THREE.Vector3(posX, 1, posZ),
        this.dungeonLevel,
        this.random.getSeed()
      );

      this.loots.push(loot);
    }

    // Return the number of enemies actually spawned
    return enemyCount;
  }

  /**
   * Populate a boss room
   */
  private populateBossRoom(room: DungeonRoom): void {
    const roomPosition = room.getPosition();

    // Create boss
    const boss = new DungeonBoss(
      this.scene,
      this.world,
      new THREE.Vector3(roomPosition.x, 2, roomPosition.z),
      this.difficulty,
      this.dungeonLevel,
      this.random.getSeed()
    );

    this.bosses.push(boss);
  }

  /**
   * Update dungeon state
   */
  update(delta: number, playerPosition: THREE.Vector3, player?: any): void {
    if (!this.isActive) return;

    // Update all enemies
    this.enemies.forEach((enemy, index) => {
      // Update enemy with player position and player reference
      enemy.update(delta, playerPosition, player);

      // Check if enemy is defeated - for Specter class we need to check the mesh
      const enemyMesh = enemy.getMesh();
      if (!enemyMesh || !this.scene.getObjectById(enemyMesh.id)) {
        // Get enemy data for scoring/rewards from userData
        const enemyData = enemyMesh?.userData || {
          points: 50,
          difficulty: this.difficulty,
          dungeonLevel: this.dungeonLevel
        };

        // Notify about enemy defeat
        this.onEnemyDefeated(enemyData);

        // Remove enemy from array
        this.enemies.splice(index, 1);

        // Check if room is cleared
        this.checkRoomCleared(playerPosition);
      }
    });

    // Update all bosses
    this.bosses.forEach((boss, index) => {
      boss.update(delta, playerPosition);

      // Check if boss is defeated
      if (boss.isDefeated()) {
        // Get boss data for scoring/rewards
        const bossData = boss.getBossData();

        // Notify about boss defeat
        this.onBossDefeated(bossData);

        // Remove boss
        boss.dispose();
        this.bosses.splice(index, 1);

        // Check if room is cleared
        this.checkRoomCleared(playerPosition);
      }
    });

    // Update all loot
    this.loots.forEach((loot, index) => {
      loot.update(delta);

      // Check if loot is collected
      if (loot.isCollected()) {
        // Get loot data
        const lootData = loot.getLootData();

        // Notify about loot collection
        this.onLootCollected(lootData);

        // Remove loot
        loot.dispose();
        this.loots.splice(index, 1);
      }
    });

    // Check which room the player is in
    this.updateCurrentRoom(playerPosition);
  }

  /**
   * Update which room the player is currently in
   */
  private updateCurrentRoom(playerPosition: THREE.Vector3): void {
    // Find which room contains the player
    let foundRoom = false;

    this.layout.rooms.forEach((room, key) => {
      if (room.containsPosition(playerPosition)) {
        // If this is a new room, mark it as visited
        if (this.layout.currentRoom !== key) {
          this.layout.currentRoom = key;
          this.layout.visitedRooms.add(key);

          // Play room entry sound
          AudioManager.playSoundEffect('doorOpen');

          console.log(`Player entered room: ${key}`);
        }

        foundRoom = true;
      }
    });

    // If player is not in any room, they might be in a corridor
    if (!foundRoom) {
      // No need to update current room
    }
  }

  /**
   * Check if the current room is cleared of enemies
   */
  private checkRoomCleared(playerPosition: THREE.Vector3): void {
    // Get current room
    const room = this.layout.rooms.get(this.layout.currentRoom);
    if (!room) return;

    // Check if room is already cleared
    if (this.layout.clearedRooms.has(this.layout.currentRoom)) return;

    // Check if there are any enemies in this room
    const enemiesInRoom = this.enemies.filter(enemy => {
      const enemyMesh = enemy.getMesh();
      return enemyMesh && room.containsPosition(enemyMesh.position);
    });

    const bossesInRoom = this.bosses.filter(boss =>
      room.containsPosition(boss.getPosition())
    );

    // If no enemies left in room, mark as cleared
    if (enemiesInRoom.length === 0 && bossesInRoom.length === 0) {
      this.layout.clearedRooms.add(this.layout.currentRoom);

      // Notify about room cleared
      this.onRoomCleared(this.layout.currentRoom);

      // Play room cleared sound
      AudioManager.playSoundEffect('success');

      console.log(`Room cleared: ${this.layout.currentRoom}`);

      // Check if this was the boss room
      if (this.layout.currentRoom === this.layout.bossRoom) {
        // Unlock exit
        const exitRoom = this.layout.rooms.get(this.layout.exitRoom);
        if (exitRoom) {
          exitRoom.unlockExit();
        }
      }

      // Check if dungeon is completed (player in exit room and boss defeated)
      if (
        this.layout.currentRoom === this.layout.exitRoom &&
        this.layout.clearedRooms.has(this.layout.bossRoom)
      ) {
        this.completeDungeon();
      }
    }
  }

  /**
   * Complete the dungeon
   */
  private completeDungeon(): void {
    // Notify about dungeon completion
    this.onDungeonCompleted();

    // Play completion sound
    AudioManager.playSoundEffect('levelComplete');

    console.log('Dungeon completed!');

    // Deactivate dungeon
    this.isActive = false;
  }

  /**
   * Fail the dungeon (player died)
   */
  public failDungeon(): void {
    // Notify about dungeon failure
    this.onDungeonFailed();

    // Play failure sound
    AudioManager.playSoundEffect('playerDeath');

    console.log('Dungeon failed!');

    // Deactivate dungeon
    this.isActive = false;
  }

  /**
   * Clear the dungeon and remove all objects
   */
  clearDungeon(): void {
    // Remove all enemies (Specter class doesn't have a dispose method, so we need to handle it differently)
    this.enemies.forEach(enemy => {
      // Remove mesh from scene
      const mesh = enemy.getMesh();
      if (mesh) {
        this.scene.remove(mesh);
      }

      // Remove physics body from world
      const body = enemy.getPhysicsBody();
      if (body) {
        this.world.removeBody(body);
      }
    });
    this.enemies = [];

    // Remove all bosses
    this.bosses.forEach(boss => boss.dispose());
    this.bosses = [];

    // Remove all loot
    this.loots.forEach(loot => loot.dispose());
    this.loots = [];

    // Remove all rooms
    this.layout.rooms.forEach(room => room.dispose());
    this.layout.rooms.clear();

    // Reset layout
    this.layout.currentRoom = '0,0';
    this.layout.bossRoom = '';
    this.layout.exitRoom = '';
    this.layout.entranceRoom = '0,0';
    this.layout.visitedRooms.clear();
    this.layout.clearedRooms.clear();

    // Set inactive
    this.isActive = false;
  }

  /**
   * Check if dungeon is active
   */
  isActiveState(): boolean {
    return this.isActive;
  }

  /**
   * Get current room
   */
  getCurrentRoom(): DungeonRoom | undefined {
    return this.layout.rooms.get(this.layout.currentRoom);
  }

  /**
   * Get all enemies
   */
  getEnemies(): Specter[] {
    return this.enemies;
  }

  /**
   * Get all bosses
   */
  getBosses(): DungeonBoss[] {
    return this.bosses;
  }

  /**
   * Get all loot
   */
  getLoot(): DungeonLoot[] {
    return this.loots;
  }

  /**
   * Get dungeon difficulty
   */
  getDifficulty(): DungeonDifficulty {
    return this.difficulty;
  }

  /**
   * Get dungeon level
   */
  getDungeonLevel(): number {
    return this.dungeonLevel;
  }
}
