import { SpecterEquipment } from '../entities/PetSpecter.js';

/**
 * Available equipment for purchase in the MerchGenie kiosk
 */
export const AVAILABLE_EQUIPMENT: SpecterEquipment[] = [
  {
    id: 'weapon-1',
    name: 'Ecto Blaster',
    type: 'weapon',
    level: 1,
    rarity: 'common',
    stats: {
      attackBonus: 5
    }
  },
  {
    id: 'weapon-2',
    name: 'Soul Reaper',
    type: 'weapon',
    level: 2,
    rarity: 'uncommon',
    stats: {
      attackBonus: 10
    }
  },
  {
    id: 'weapon-3',
    name: 'Phantom Blade',
    type: 'weapon',
    level: 3,
    rarity: 'rare',
    stats: {
      attackBonus: 15,
      speedBonus: 1
    }
  },
  {
    id: 'armor-1',
    name: 'Spectral Shield',
    type: 'armor',
    level: 1,
    rarity: 'common',
    stats: {
      defenseBonus: 3
    }
  },
  {
    id: 'armor-2',
    name: 'Ectoplasmic Armor',
    type: 'armor',
    level: 2,
    rarity: 'uncommon',
    stats: {
      defenseBonus: 7
    }
  },
  {
    id: 'armor-3',
    name: 'Ethereal Plate',
    type: 'armor',
    level: 3,
    rarity: 'rare',
    stats: {
      defenseBonus: 12,
      attackBonus: 2
    }
  },
  {
    id: 'utility-1',
    name: 'Speed Wisp',
    type: 'utility',
    level: 1,
    rarity: 'common',
    stats: {
      speedBonus: 2
    }
  },
  {
    id: 'utility-2',
    name: 'Phantom Charm',
    type: 'utility',
    level: 2,
    rarity: 'uncommon',
    stats: {
      speedBonus: 3,
      defenseBonus: 2
    }
  },
  {
    id: 'utility-3',
    name: 'Ethereal Amulet',
    type: 'utility',
    level: 3,
    rarity: 'rare',
    stats: {
      speedBonus: 5,
      attackBonus: 3
    }
  }
];

/**
 * Get equipment price based on rarity and level
 */
export function getEquipmentPrice(equipment: SpecterEquipment): number {
  const rarityMultiplier = {
    'common': 1,
    'uncommon': 2,
    'rare': 4,
    'epic': 8,
    'legendary': 16
  };
  
  const basePrice = 100;
  return basePrice * rarityMultiplier[equipment.rarity] * equipment.level;
}

/**
 * Get color for equipment rarity
 */
export function getEquipmentColor(rarity: string): string {
  switch (rarity) {
    case 'common':
      return 'text-gray-200';
    case 'uncommon':
      return 'text-green-400';
    case 'rare':
      return 'text-blue-400';
    case 'epic':
      return 'text-purple-400';
    case 'legendary':
      return 'text-orange-400';
    default:
      return 'text-white';
  }
}
