import * as THREE from 'three';
import GameEngine from '../engine/GameEngine.js';
import { MessageType, NetworkMessage, RenderInstruction, RenderInstructionType, PowerupType } from '@shared/schema.js';
import { addMessageHandler, sendMessage } from '@/utils/unifiedWebSocket.js';

/**
 * TournamentBattleClient
 *
 * Handles client-side rendering of tournament battles based on server instructions.
 * Manages spectator mode and battle visualization.
 */
export class TournamentBattleClient {
  private gameEngine: GameEngine;
  private currentBattleId: string | null = null;
  private battleParticipants: Map<string | number, any> = new Map();
  private battleEffects: Map<string, THREE.Object3D> = new Map();
  private battleLog: any[] = [];
  private isSpectating: boolean = false;
  private connectionId: string | null = null; // Added to store connection ID

  /**
   * Constructor
   * @param gameEngine Reference to the game engine
   */
  constructor(gameEngine: GameEngine) {
    this.gameEngine = gameEngine;

    // Message listeners will be set up when a connection is established
    // this.setupMessageListeners(); // Removed from constructor
  }

  /**
   * Set the connection ID and set up listeners
   * @param connectionId The WebSocket connection ID
   */
  public setConnection(connectionId: string): void {
    if (this.connectionId === connectionId) {
      // Already connected with this ID
      return;
    }
    this.connectionId = connectionId;
    console.log(`TournamentBattleClient: Setting up listeners for connection ID: ${connectionId}`);
    this.setupMessageListeners(connectionId);
  }

  /**
   * Set up WebSocket message listeners for a specific connection
   * @param connectionId The WebSocket connection ID
   */
  private setupMessageListeners(connectionId: string): void {
    // Define the handler function
    const messageHandler = (message: NetworkMessage) => {
      if (!message) return;

      console.log(`TournamentBattleClient (${connectionId}) received message:`, message.type, message);

      // Ensure the message is for the current battle if applicable
      if (message.data?.battleId && this.currentBattleId && message.data.battleId !== this.currentBattleId) {
        console.log(`TournamentBattleClient: Ignoring message for different battle ${message.data.battleId} (current: ${this.currentBattleId})`);
        return;
      }

      switch (message.type) {
        case MessageType.TournamentBattleUpdate:
          this.handleBattleUpdate(message.data);
          break;

        case MessageType.TournamentBattleRenderInstructions:
          // Ensure we pass only the instructions array, not the whole data object
          if (message.data && Array.isArray(message.data.instructions)) {
            this.handleRenderInstructions(message.data.instructions);
          } else {
            console.error('Received TournamentBattleRenderInstructions with invalid data format:', message.data);
          }
          break;

        case MessageType.TournamentBattleJoin:
          console.log('Successfully joined battle as spectator:', message.data);
          // Potentially update internal state based on join confirmation
          if (message.data?.success && message.data?.battleId === this.currentBattleId) {
             // Maybe set a flag or trigger initial state fetch
          }
          break;
          
        // Handle Pong messages if needed for latency checks, etc.
        case MessageType.Pong:
          // console.log(`Received Pong from server:`, message.data);
          break;
      }
    };

    // Register the handler using the unified WebSocket utility
    addMessageHandler(connectionId, messageHandler);

    // Store the handler reference if we need to remove it later (e.g., on leaveBattle)
    // Note: We might need a way to map connectionId to its handler if multiple connections are possible.
    (this as any)._messageHandler = messageHandler; 

    // Remove the old document event listeners if they exist
    // document.removeEventListener('websocketMessage', ...);
    // document.removeEventListener('rawWebSocketMessage', ...);
  }

  /**
   * Handle battle update message from server
   * @param data The battle update data
   */
  public handleBattleUpdate(data: any): void {
    console.log('BATTLE UPDATE: Handling battle update:', data);

    try {
      // Handle different data formats
      let battle;
      if (data.battle) {
        battle = data.battle;
        console.log('BATTLE UPDATE: Using data.battle format');
      } else if (data.battleId) {
        battle = data;
        console.log('BATTLE UPDATE: Using data format with battleId');
      } else {
        console.error('BATTLE UPDATE: Invalid battle update data format:', data);
        return;
      }

      // Check if this is a test battle (UUID format)
      const battleId = battle.id || battle.battleId;
      // const isTestBattle = battleId && battleId.length >= 36; // No longer needed for special handling here

      console.log(`BATTLE UPDATE: Processing battle data:`, battle);

      // Store the battle ID
      this.currentBattleId = battle.id || battle.battleId;
      console.log(`BATTLE UPDATE: Set current battle ID to: ${this.currentBattleId}`);

      // Set spectating flag
      this.isSpectating = true;

      // Update battle log
      if (battle.battleLog && Array.isArray(battle.battleLog)) {
        this.battleLog = battle.battleLog;

        // Display the latest log entry if available
        const latestLog = battle.battleLog[battle.battleLog.length - 1];
        if (latestLog) {
          this.displayBattleLogEntry(latestLog);
        }
      }

      // Update battle participants
      if (battle.participants && Array.isArray(battle.participants)) {
        console.log(`Processing ${battle.participants.length} battle participants`);

        // Track processed participants to detect removed ones
        const processedParticipantIds = new Set<string | number>();

        // Add new participants
        battle.participants.forEach((participant: {
          id: string | number;
          health?: number;
          maxHealth?: number;
          position?: { x: number; y: number; z: number };
          rotation?: { x: number; y: number; z: number };
          petName?: string;
        }) => {
          if (!participant || !participant.id) {
            console.warn('Invalid participant data:', participant);
            return;
          }

          // Track this participant as processed
          processedParticipantIds.add(participant.id);

          console.log(`Processing participant ${participant.id}:`, participant);

          // Store participant data
          const existingParticipant = this.battleParticipants.get(participant.id);

          if (existingParticipant) {
            console.log(`Updating existing participant ${participant.id}`);
            // Update existing participant data
            existingParticipant.health = participant.health || existingParticipant.health;
            existingParticipant.maxHealth = participant.maxHealth || existingParticipant.maxHealth;

            // Update position and rotation if provided
            if (participant.position && existingParticipant.object3D) {
              existingParticipant.object3D.position.set(
                participant.position.x || 0,
                participant.position.y || 1,
                participant.position.z || 0
              );
              console.log(`Updated position for ${participant.id} to:`, participant.position);

              // Update stored position
              existingParticipant.position = participant.position;
            }

            if (participant.rotation && existingParticipant.object3D) {
              existingParticipant.object3D.rotation.set(
                participant.rotation.x || 0,
                participant.rotation.y || 0,
                participant.rotation.z || 0
              );
              console.log(`Updated rotation for ${participant.id} to:`, participant.rotation);

              // Update stored rotation
              existingParticipant.rotation = participant.rotation;
            }
          } else {
            console.log(`Creating new participant ${participant.id}`);

            // Create entity in the scene
            this.createEntityInScene(
              participant.id,
              participant.position,
              participant.rotation,
              participant.petName || `Pet ${participant.id}`
            );
          }
        });

        // Remove participants that are no longer in the battle
        this.battleParticipants.forEach((participant, id) => {
          if (!processedParticipantIds.has(id)) {
            console.log(`Removing participant ${id} as they are no longer in the battle`);

            // Remove from scene if it has a 3D object
            if (participant.object3D) {
              const scene = this.gameEngine.getScene();
              if (scene) {
                scene.remove(participant.object3D);
              }
            }

            // Remove from participants map
            this.battleParticipants.delete(id);

            // Display message in battle log
            this.displayBattleLogEntry({
              type: 'system',
              message: `${participant.petName || 'Pet ' + id} has left the battle!`,
              timestamp: Date.now()
            });
          }
        });
      } else {
        console.log('No participants data in battle update');
      }

      // Process render instructions if available
      if (battle.renderInstructions && Array.isArray(battle.renderInstructions)) {
        console.log(`Processing ${battle.renderInstructions.length} render instructions from battle update`);
        this.handleRenderInstructions(battle.renderInstructions);
      }

      // Update battle status
      if (battle.status) {
        console.log(`Battle status updated to: ${battle.status}`);

        // Handle battle completion
        if (battle.status === 'completed' && battle.winnerId) {
          console.log(`Battle completed with winner: ${battle.winnerId}`);

          // Find the winner entity
          const winner = this.battleParticipants.get(battle.winnerId);
          if (winner) {
            // Display victory message
            this.displayBattleLogEntry({
              type: 'victory',
              message: `${winner.petName || 'Pet ' + battle.winnerId} wins the battle!`,
              timestamp: Date.now()
            });

            // Create victory effect
            this.createVictoryEffect(winner);
          }
        }
      }

      console.log(`Battle update received for battle ${battle.id || battle.battleId}, status: ${battle.status || 'unknown'}`);
    } catch (error) {
      console.error('Error processing battle update:', error);
    }
  }

  /**
   * Create entities directly from battle update data
   * This is a more aggressive approach for test battles to ensure entities are created
   * @param participants The battle participants data
   */
  private createEntitiesFromBattleUpdate(participants: any[]): void {
    console.log('Creating entities directly from battle update data:', participants);

    // Ensure we have a scene
    if (!this.gameEngine.getScene()) {
      console.error('BATTLE UPDATE ERROR: No scene available for creating entities from battle update. Arena should have been created by GameEngine.');
      // REMOVED: Fallback arena creation logic - NO FALLBACKS WILL CORRECT A FAILURE. CORRECT. DO NOT FALL BACKWARD EVER.
      // this.gameEngine.createDedicatedPvpArena().then(() => {
      //   // Try again after scene is created
      //   if (this.gameEngine.getScene()) {
      //     console.log('Scene created, now creating entities from battle update');
      //     this.createEntitiesFromBattleUpdate(participants);
      //   }
      // });
      return;
    }

    // Process each participant
    participants.forEach(participant => {
      const { id, petName, position, rotation } = participant;

      // Check if entity already exists in the scene
      const existingParticipant = this.battleParticipants.get(id);
      if (!existingParticipant || !existingParticipant.object3D) {
        console.log(`Creating entity ${id} directly from battle update`);

        // Create entity in scene
        this.createEntityInScene(id, position, rotation, petName);
      }
    });
  }

  /**
   * Join a battle as a spectator
   * @param battleId The battle ID to join
   */
  public joinBattleAsSpectator(battleId: string): void {
    console.log(`TournamentBattleClient: Joining battle ${battleId} as spectator`);

    // Store the battle ID
    this.currentBattleId = battleId;
    this.isSpectating = true;

    // Send join message to server
    this.sendWebSocketMessage({
      type: MessageType.TournamentBattleJoin,
      data: {
        battleId,
        isSpectator: true,
        userId: localStorage.getItem('walletAddress') || 'anonymous_spectator'
      },
      timestamp: Date.now(),
      sender: 'client'
    });

    // For test battles (UUID format), create test entities immediately
    // This ensures we see something even if the WebSocket connection fails
    if (battleId.length >= 36) {
      console.log('TournamentBattleClient: Creating test battle entities for local visualization');
      setTimeout(() => {
        this.createTestBattleEntities();
      }, 1000);
    }
  }

  /**
   * Create test battle entities for local visualization
   * This is used when we can't connect to the WebSocket server
   */
  private createTestBattleEntities(): void {
    console.log('TournamentBattleClient: Creating test battle entities');
    
    // Create two test specters for the battle
    const specterPositions = [
      new THREE.Vector3(-5, 1, 0),
      new THREE.Vector3(5, 1, 0)
    ];

    // Clear existing participants
    this.battleParticipants.clear();

    // Add two test participants
    for (let i = 0; i < 2; i++) {
      const testId = `test-specter-${i}`;
      const position = specterPositions[i];
      
      // Create a simple sphere as a placeholder
      const geometry = new THREE.SphereGeometry(1, 16, 16);
      const material = new THREE.MeshStandardMaterial({ 
        color: i === 0 ? 0x3399ff : 0xff3366,
        emissive: i === 0 ? 0x3399ff : 0xff3366,
        emissiveIntensity: 0.3
      });
      
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(position);
      
      // Add to scene
      this.gameEngine.getScene()?.add(mesh);
      
      // Store in battle participants
      this.battleParticipants.set(testId, {
        id: testId,
        mesh,
        position,
        health: 100,
        team: i
      });
      
      console.log(`TournamentBattleClient: Created test specter ${testId} at position ${position.x}, ${position.y}, ${position.z}`);
    }
    
    // Set up a simple animation loop for the test entities
    this.animateTestEntities();
  }
  
  /**
   * Animate the test entities to simulate a battle
   */
  private animateTestEntities(): void {
    if (!this.isSpectating || !this.currentBattleId) return;
    
    let frameCount = 0;
    
    const animate = () => {
      if (!this.isSpectating || !this.currentBattleId) return;
      
      frameCount++;
      
      // Move the entities in a simple pattern
      this.battleParticipants.forEach((entity, id) => {
        if (entity.mesh) {
          // Make them float up and down
          entity.mesh.position.y = entity.position.y + Math.sin(frameCount * 0.05) * 0.5;
          
          // Make them face each other
          entity.mesh.lookAt(entity.mesh.position.x > 0 ? new THREE.Vector3(-5, 1, 0) : new THREE.Vector3(5, 1, 0));
          
          // Every 2 seconds, create a simple "attack" effect
          if (frameCount % 120 === (id === 'test-specter-0' ? 0 : 60)) {
            this.createSimpleAttackEffect(entity.mesh.position, entity.mesh.position.x > 0 ? -1 : 1);
          }
        }
      });
      
      requestAnimationFrame(animate);
    };
    
    // Start the animation
    animate();
  }
  
  /**
   * Create a simple attack effect for test battles
   */
  private createSimpleAttackEffect(position: THREE.Vector3, direction: number): void {
    const scene = this.gameEngine.getScene();
    if (!scene) return;
    
    // Create a simple projectile
    const geometry = new THREE.SphereGeometry(0.3, 8, 8);
    const material = new THREE.MeshBasicMaterial({ 
      color: direction > 0 ? 0x3399ff : 0xff3366,
      transparent: true,
      opacity: 0.7
    });
    
    const projectile = new THREE.Mesh(geometry, material);
    projectile.position.copy(position);
    scene.add(projectile);
    
    // Animate the projectile
    let frame = 0;
    const maxFrames = 30;
    
    const animateProjectile = () => {
      frame++;
      
      // Move in direction
      projectile.position.x += direction * 0.3;
      
      // Fade out near the end
      if (frame > maxFrames * 0.7) {
        const opacity = 0.7 * (1 - (frame - maxFrames * 0.7) / (maxFrames * 0.3));
        (projectile.material as THREE.MeshBasicMaterial).opacity = opacity;
      }
      
      if (frame < maxFrames) {
        requestAnimationFrame(animateProjectile);
      } else {
        // Clean up
        scene.remove(projectile);
        if (geometry) geometry.dispose();
        if (material) material.dispose();
      }
    };
    
    // Start animation
    animateProjectile();
  }

  /**
   * Leave the current battle
   */
  public leaveBattle(): void {
    if (!this.isSpectating || !this.currentBattleId) {
      return;
    }

    console.log(`Leaving battle ${this.currentBattleId}`);

    // Send leave message to server
    this.sendWebSocketMessage({
      type: MessageType.TournamentBattleLeave,
      data: {
        battleId: this.currentBattleId
      },
      timestamp: Date.now(),
      sender: 'client'
    });

    // Reset state
    this.isSpectating = false;
    this.currentBattleId = null;

    // Disable spectator mode in game engine
    this.gameEngine.setSpectatorMode(false);

    // Clear battle participants and effects
    this.battleParticipants.clear();
    this.clearBattleEffects();

    // Clear battle log
    this.battleLog = [];
  }

  /**
   * Clear all battle effects
   */
  private clearBattleEffects(): void {
    const scene = this.gameEngine.getScene();
    if (!scene) return;

    // Remove all effects from the scene
    this.battleEffects.forEach((effect) => {
      scene.remove(effect);
    });

    // Clear the map
    this.battleEffects.clear();
  }

  /**
   * Send a WebSocket message
   * @param message The message to send
   */
  private sendWebSocketMessage(message: NetworkMessage): void {
    if (!this.connectionId) {
      console.error('TournamentBattleClient: Cannot send WebSocket message, connectionId is null');
      return;
    }

    console.log(`TournamentBattleClient (${this.connectionId}): Sending WebSocket message:`, message.type, message.data);

    // Add user ID to the message data if not already present
    const userId = localStorage.getItem('walletAddress') || 'anonymous_spectator';
    const dataWithUser = {
      ...(message.data || {}),
      userId: message.data?.userId || userId,
    };

    // Use the unified sendMessage function
    const success = sendMessage(this.connectionId, message.type, dataWithUser);

    if (!success) {
      console.error(`TournamentBattleClient (${this.connectionId}): Failed to send WebSocket message of type ${message.type}`);
      // Handle send failure (e.g., attempt reconnect, show error)
      // We might need to check connection status here
    }
  }

  /**
   * Display a battle log entry on the screen
   * @param logEntry The log entry to display
   */
  private displayBattleLogEntry(logEntry: any): void {
    if (!logEntry || !logEntry.message) return;

    // Create a log element
    const logElement = document.createElement('div');
    logElement.className = 'battle-log-entry';
    logElement.textContent = logEntry.message;

    // Add appropriate styling based on log type
    if (logEntry.type === 'attack') {
      logElement.classList.add('battle-log-attack');
    } else if (logEntry.type === 'powerup') {
      logElement.classList.add('battle-log-powerup');
    } else if (logEntry.type === 'damage') {
      logElement.classList.add('battle-log-damage');
    } else if (logEntry.type === 'victory') {
      logElement.classList.add('battle-log-victory');
    }

    // Add the log element to the DOM
    const logContainer = document.getElementById('battle-log-container');
    if (logContainer) {
      logContainer.appendChild(logElement);

      // Scroll to the bottom
      logContainer.scrollTop = logContainer.scrollHeight;

      // Remove old entries if there are too many
      while (logContainer.children.length > 20) {
        logContainer.removeChild(logContainer.children[0]);
      }
    }

    // Auto-remove the log entry after a few seconds
    setTimeout(() => {
      if (logElement.parentNode) {
        logElement.parentNode.removeChild(logElement);
      }
    }, 5000);
  }

  /**
   * Handle render instructions from the server
   * @param instructions The render instructions to process
   */
  public handleRenderInstructions(instructions: RenderInstruction[]): void {
    if (!this.gameEngine) {
      console.error('GameEngine not available for rendering instructions');
      return;
    }

    // If no instructions or empty array, do nothing
    if (!instructions || instructions.length === 0) {
      return;
    }

    console.log(`Received ${instructions.length} render instructions for processing`);

    // Determine if this is a test battle (UUID format)
    const isTestBattle = this.currentBattleId && this.currentBattleId.length >= 36;

    if (isTestBattle) {
      console.log('Processing test battle render instructions');
      this.handleTestBattleRenderInstructions(instructions);
    } else {
      console.log('Processing regular battle render instructions');
      this.handleRegularBattleRenderInstructions(instructions);
    }
  }

  private handleTestBattleRenderInstructions(instructions: RenderInstruction[]): void {
    // Ensure scene is ready
    const ensureSceneReady = async () => {
      if (!this.gameEngine.getScene()) {
        console.error('No scene available for test battle rendering instructions');
        return false;
      }
      return true;
    };

    // Extract spawn instructions for initial entity creation
    const spawnInstructions = instructions.filter(
      instr => instr.type === RenderInstructionType.SPAWN_ENTITY
    );

    // Extract move instructions for entity movement
    const moveInstructions = instructions.filter(
      instr => instr.type === RenderInstructionType.MOVE_ENTITY
    );

    // Extract other instruction types
    const attackInstructions = instructions.filter(
      instr => instr.type === RenderInstructionType.ATTACK
    );

    const damageInstructions = instructions.filter(
      instr => instr.type === RenderInstructionType.DAMAGE
    );

    const powerupInstructions = instructions.filter(
      instr => instr.type === RenderInstructionType.USE_POWERUP
    );

    const victoryInstructions = instructions.filter(
      instr => instr.type === RenderInstructionType.VICTORY
    );

    // Process all instructions in the correct order
    ensureSceneReady().then(sceneReady => {
      if (!sceneReady) {
        console.error('Failed to ensure scene was ready for test battle rendering');
        return;
      }

      // First clear entities that are being respawned
      // This prevents duplicates by removing the old entities before creating new ones
      spawnInstructions.forEach(instruction => {
        if (instruction.data?.entityId) {
          const entityId = instruction.data.entityId;
          const existingEntity = this.battleParticipants.get(entityId);
          
          // Remove the entity from the scene if it exists
          if (existingEntity && existingEntity.object3D) {
            console.log(`Removing existing entity ${entityId} before respawning`);
            // Remove from scene
            this.gameEngine.getScene()?.remove(existingEntity.object3D);
            // Clear any animations or intervals
            if (existingEntity.animationId) {
              cancelAnimationFrame(existingEntity.animationId);
            }
            // Remove from participants map
            this.battleParticipants.delete(entityId);
          }
        }
      });

      // Process spawn instructions first to ensure entities exist
      console.log(`Processing ${spawnInstructions.length} spawn instructions`);
      this.directlyCreateEntitiesFromInstructions(spawnInstructions);

      // Process move instructions to update entity positions
      moveInstructions.forEach(instruction => {
        this.handleMoveEntityInstruction(instruction);
      });

      // Process attack instructions
      attackInstructions.forEach(instruction => {
        this.handleAttackInstruction(instruction);
      });

      // Process damage instructions
      damageInstructions.forEach(instruction => {
        this.handleDamageInstruction(instruction);
      });

      // Process powerup instructions
      powerupInstructions.forEach(instruction => {
        this.handleUsePowerupInstruction(instruction);
      });

      // Process victory instructions
      victoryInstructions.forEach(instruction => {
        this.handleVictoryInstruction(instruction);
      });

      // Perform final verification
      this.verifyEntitiesExist(spawnInstructions);
    });
  }

  /**
   * Handle render instructions specifically for test battles with enhanced initialization
   * @param instructions The render instructions to process
   */
  private handleRegularBattleRenderInstructions(instructions: RenderInstruction[]): void {
    // Ensure we have a scene ready
    const ensureSceneReady = async () => {
      if (!this.gameEngine.getScene()) {
        console.error('No scene available for rendering instructions. Waiting for GameEngine.');
        // Short delay to allow GameEngine time to create the scene
        await new Promise(resolve => setTimeout(resolve, 100)); 
        return !!this.gameEngine.getScene(); // Check again
      }
      return true;
    };

    // Process instructions after ensuring scene is ready
    ensureSceneReady().then(sceneReady => {
      if (!sceneReady) {
        console.error('Failed to create scene for render instructions');
        return;
      }

      // Process the instructions now that we have a scene
      this.processRenderInstructions(instructions);
    }).catch(error => {
      console.error('Error ensuring scene ready:', error);
    });
  }

  /**
   * Ensure the scene is ready for rendering
   * @returns Promise that resolves to true if scene is ready, false otherwise
   */
  private async ensureSceneReady(): Promise<boolean> {
    if (this.gameEngine.getScene()) {
      return true;
    }

    // If the scene isn't ready, it's an error state, as GameEngine should handle creation.
    console.error('ENSURE SCENE READY ERROR: No scene available. GameEngine should have created the PVP arena.');
    return false; // Indicate scene is not ready
  }

  /**
   * Process render instructions after ensuring scene is ready
   * @param instructions The render instructions to process
   */
  private processRenderInstructions(instructions: RenderInstruction[]): void {
    if (!instructions || instructions.length === 0) {
      return;
    }

    console.log(`Processing ${instructions.length} render instructions`);

    // Process each instruction
    instructions.forEach((instruction, index) => {
      if (!instruction || !instruction.type) {
        console.warn(`[Instruction ${index}] Invalid instruction:`, instruction);
        return;
      }

      console.log(`[Instruction ${index}] Processing ${instruction.type}:`, instruction);

      try {
        switch (instruction.type) {
          case RenderInstructionType.SPAWN_ENTITY:
            if (!instruction.data?.entityId) {
              console.error(`[SPAWN_ENTITY] Missing entityId in instruction:`, instruction);
              return;
            }
            console.log(`[SPAWN_ENTITY] Spawning entity ${instruction.data.entityId}`);
            this.handleSpawnEntityInstruction(instruction);
            break;

          case RenderInstructionType.MOVE_ENTITY:
            if (!instruction.data?.entityId) {
              console.error(`[MOVE_ENTITY] Missing entityId in instruction:`, instruction);
              return;
            }
            console.log(`[MOVE_ENTITY] Moving entity ${instruction.data.entityId}`);
            this.handleMoveEntityInstruction(instruction);
            break;

          case RenderInstructionType.ATTACK:
            if (!instruction.data?.attackerId || !instruction.data?.targetId) {
              console.error(`[ATTACK] Missing attackerId or targetId in instruction:`, instruction);
              return;
            }
            console.log(`[ATTACK] Entity ${instruction.data.attackerId} attacking ${instruction.data.targetId}`);
            this.handleAttackInstruction(instruction);
            break;

          case RenderInstructionType.USE_POWERUP:
            if (!instruction.data?.entityId || !instruction.data?.powerupType) {
              console.error(`[USE_POWERUP] Missing entityId or powerupType in instruction:`, instruction);
              return;
            }
            console.log(`[USE_POWERUP] Entity ${instruction.data.entityId} using powerup ${instruction.data.powerupType}`);
            this.handleUsePowerupInstruction(instruction);
            break;

          case RenderInstructionType.DAMAGE:
          case RenderInstructionType.UPDATE_HEALTH:
            if (!instruction.data?.entityId) {
              console.error(`[DAMAGE/UPDATE_HEALTH] Missing entityId in instruction:`, instruction);
              return;
            }
            console.log(`[DAMAGE/UPDATE_HEALTH] Entity ${instruction.data.entityId} health update`);
            this.handleDamageInstruction(instruction);
            break;

          case RenderInstructionType.VICTORY:
            if (!instruction.data?.winnerId) {
              console.error(`[VICTORY] Missing winnerId in instruction:`, instruction);
              return;
            }
            console.log(`[VICTORY] Entity ${instruction.data.winnerId} is victorious`);
            this.handleVictoryInstruction(instruction);
            break;

          default:
            console.warn(`[UNKNOWN] Unknown render instruction type:`, instruction.type);
        }
      } catch (error) {
        console.error(`[ERROR] Error processing instruction of type ${instruction.type}:`, error);

        // For test battles, try to recover from errors
        const isTestBattle = this.currentBattleId && this.currentBattleId.length >= 36;
        if (isTestBattle) {
          console.log(`[RECOVERY] Attempting to recover from ${instruction.type} error in test battle`);
          this.attemptErrorRecovery(instruction);
        }
      }
    });
  }

  /**
   * Attempt to recover from an error in processing an instruction
   * @param instruction The instruction that caused the error
   */
  private attemptErrorRecovery(instruction: RenderInstruction): void {
    try {
      // Handle different instruction types for recovery
      switch (instruction.type) {
        case RenderInstructionType.SPAWN_ENTITY:
          // Try again with more defensive handling for spawn entity
          const { entityId, position, rotation, name } = instruction.data;
          if (entityId) {
            // Ensure we have valid position and rotation
            const safePosition = position ?
              { x: position.x || 0, y: position.y || 1, z: position.z || 0 } :
              { x: 0, y: 1, z: 0 };

            const safeRotation = rotation ?
              { x: rotation.x || 0, y: rotation.y || 0, z: rotation.z || 0 } :
              { x: 0, y: 0, z: 0 };

            console.log(`[RECOVERY] Creating entity ${entityId} with safe position:`, safePosition);
            this.createEntityInScene(entityId, safePosition, safeRotation, name);
          }
          break;

        case RenderInstructionType.MOVE_ENTITY:
          // Try to recover move entity instructions
          const moveData = instruction.data;
          if (moveData.entityId) {
            const entity = this.battleParticipants.get(moveData.entityId);
            if (!entity) {
              // Entity doesn't exist, try to create it
              const safePosition = moveData.position ?
                { x: moveData.position.x || 0, y: moveData.position.y || 1, z: moveData.position.z || 0 } :
                { x: 0, y: 1, z: 0 };

              const safeRotation = moveData.rotation ?
                { x: moveData.rotation.x || 0, y: moveData.rotation.y || 0, z: moveData.rotation.z || 0 } :
                { x: 0, y: 0, z: 0 };

              console.log(`[RECOVERY] Creating missing entity ${moveData.entityId} for move instruction`);
              this.createEntityInScene(moveData.entityId, safePosition, safeRotation);
            }
          }
          break;

        case RenderInstructionType.ATTACK:
          // Try to recover attack instructions
          const attackData = instruction.data;
          if (attackData.attackerId && attackData.targetId) {
            // Check if entities exist
            const attacker = this.battleParticipants.get(attackData.attackerId);
            const target = this.battleParticipants.get(attackData.targetId);

            // Create missing entities if needed
            if (!attacker) {
              console.log(`[RECOVERY] Creating missing attacker entity ${attackData.attackerId}`);
              this.createEntityInScene(attackData.attackerId, { x: -5, y: 1, z: 0 }, { x: 0, y: 0, z: 0 });
            }

            if (!target) {
              console.log(`[RECOVERY] Creating missing target entity ${attackData.targetId}`);
              this.createEntityInScene(attackData.targetId, { x: 5, y: 1, z: 0 }, { x: 0, y: Math.PI, z: 0 });
            }
          }
          break;
          
        case RenderInstructionType.USE_POWERUP:
          // Try to recover powerup instructions
          const powerupData = instruction.data;
          if (powerupData.entityId) {
            // Check if entity exists
            const entity = this.battleParticipants.get(powerupData.entityId);
            if (!entity) {
              console.log(`[RECOVERY] Creating missing entity ${powerupData.entityId} for powerup instruction`);
              this.createEntityInScene(powerupData.entityId, { x: 0, y: 1, z: 0 }, { x: 0, y: 0, z: 0 });
            }
          }
          break;
          
        case RenderInstructionType.DAMAGE:
        case RenderInstructionType.UPDATE_HEALTH:
          // Try to recover damage instructions
          const damageData = instruction.data;
          if (damageData.entityId) {
            // Check if entity exists
            const entity = this.battleParticipants.get(damageData.entityId);
            if (!entity) {
              console.log(`[RECOVERY] Creating missing entity ${damageData.entityId} for damage instruction`);
              this.createEntityInScene(damageData.entityId, { x: 0, y: 1, z: 0 }, { x: 0, y: 0, z: 0 });
            }
          }
          break;
      }
    } catch (recoveryError) {
      console.error(`[RECOVERY] Failed to recover from ${instruction.type} error:`, recoveryError);
    }
  }

  /**
   * Handle spawn entity instruction
   * @param instruction The spawn entity instruction
   */
  private handleSpawnEntityInstruction(instruction: RenderInstruction): void {
    if (!instruction.data) return;
    
    const { entityId, position, rotation, name } = instruction.data;
    
    console.log(`Handling spawn entity instruction for ${entityId}`, instruction.data);
    
    // Check if entity already exists in the scene
    const existingEntity = this.battleParticipants.get(entityId);
    
    // If entity exists, simply update its position and rotation
    if (existingEntity && existingEntity.object3D) {
      console.log(`Entity ${entityId} already exists, updating instead of creating new`);
      
      // Update position if provided
      if (position && existingEntity.object3D) {
        existingEntity.object3D.position.set(
          position.x || 0,
          position.y || 1,
          position.z || 0
        );
        // Update stored position
        existingEntity.position = position;
      }
      
      // Update rotation if provided
      if (rotation && existingEntity.object3D) {
        existingEntity.object3D.rotation.set(
          rotation.x || 0,
          rotation.y || 0,
          rotation.z || 0
        );
        // Update stored rotation
        existingEntity.rotation = rotation;
      }
      
      return;
    }
    
    // If entity doesn't exist, create it
    this.createEntityInScene(entityId, position, rotation, name);
  }

  /**
   * Handle move entity instruction
   * @param instruction The move entity instruction
   */
  private handleMoveEntityInstruction(instruction: RenderInstruction): void {
    if (!instruction.data) return;
    
    const { entityId, position, rotation } = instruction.data;
    
    // Get the entity from the map
    const entity = this.battleParticipants.get(entityId);
    
    if (!entity) {
      console.warn(`Entity ${entityId} not found for movement instruction`);
      // Create the entity if it doesn't exist (recovery mechanism)
      this.createEntityInScene(entityId, position, rotation);
      return;
    }
    
    // Ensure the entity has a 3D object
    if (!entity.object3D) {
      console.warn(`Entity ${entityId} has no 3D object`);
      return;
    }
    
    // Update position if provided
    if (position) {
      // Clear any existing position animation
      if (entity.positionAnimation) {
        cancelAnimationFrame(entity.positionAnimation);
        entity.positionAnimation = null;
      }
      
      // Set the new position directly
      entity.object3D.position.set(
        position.x || 0,
        position.y || 1,
        position.z || 0
      );
      
      // Update stored position
      entity.position = position;
    }
    
    // Update rotation if provided
    if (rotation) {
      entity.object3D.rotation.set(
        rotation.x || 0,
        rotation.y || 0,
        rotation.z || 0
      );
      
      // Update stored rotation
      entity.rotation = rotation;
    }
  }

  /**
   * Create an entity in the scene
   * @param entityId The entity ID
   * @param position The initial position
   * @param rotation The initial rotation
   * @param name The entity name
   */
  private async createEntityInScene(entityId: string | number, position: any, rotation: any, name?: string): Promise<void> {
    console.log(`ENTITY CREATION: Creating entity ${entityId} in scene at position:`, position);

    // Get the current scene from the game engine
    let activeScene = this.gameEngine.getScene();

    // Check if the scene exists
    if (!activeScene) {
      console.error(`ENTITY CREATION ERROR: Scene not found for entity ${entityId}. Waiting for GameEngine to create the arena.`);
      return; // Exit if scene doesn't exist
    }

    // Convert entityId to string for consistent handling
    const entityIdStr = String(entityId);

    // Check if entity already exists
    const existingEntity = this.battleParticipants.get(entityId);
    if (existingEntity && existingEntity.object3D) {
      console.log(`ENTITY CREATION: Entity ${entityId} already exists, updating position and rotation`);
      // Update existing entity position and rotation
      if (position) {
        existingEntity.object3D.position.set(
          position.x || 0,
          position.y || 1,
          position.z || 0
        );
      }
      if (rotation) {
        existingEntity.object3D.rotation.set(
          rotation.x || 0,
          rotation.y || 0,
          rotation.z || 0
        );
      }
      return;
    }

    console.log(`ENTITY CREATION: Creating new entity ${entityId} in scene with name: ${name || 'unnamed'}`);
    console.log(`ENTITY CREATION: Position: ${JSON.stringify(position)}, Rotation: ${JSON.stringify(rotation)}`);

    // Ensure position and rotation are valid
    const validPosition = {
      x: position?.x || 0,
      y: position?.y || 1,
      z: position?.z || 0
    };

    const validRotation = {
      x: rotation?.x || 0,
      y: rotation?.y || 0,
      z: rotation?.z || 0
    };

    try {
      // Create a new entity object - use a more visible representation
      // Use a colored sphere with emissive material to make it stand out
      const geometry = new THREE.SphereGeometry(1.5, 16, 16);

      // Alternate colors based on entity ID to distinguish participants
      const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff];
      const colorIndex = Math.abs(
        entityIdStr.charCodeAt(0) + (entityIdStr.charCodeAt(1) || 0)
      ) % colors.length;
      const color = colors[colorIndex];

      const material = new THREE.MeshStandardMaterial({
        color: color,
        emissive: color,
        emissiveIntensity: 0.5,
        metalness: 0.8,
        roughness: 0.2
      });

      const object3D = new THREE.Mesh(geometry, material);
      object3D.castShadow = true;
      object3D.receiveShadow = true;

      // Set position and rotation using validated values
      object3D.position.set(validPosition.x, validPosition.y, validPosition.z);
      object3D.rotation.set(validRotation.x, validRotation.y, validRotation.z);

      console.log(`ENTITY CREATION: Set entity position to: (${validPosition.x}, ${validPosition.y}, ${validPosition.z})`);

      // Set initial scale to full size for test battles to ensure visibility
      const isTestBattle = this.currentBattleId && this.currentBattleId.length >= 36;
      if (isTestBattle) {
        // For test battles, start at full scale to ensure visibility
        object3D.scale.set(1.0, 1.0, 1.0);
        console.log(`ENTITY CREATION: Test battle entity set to full scale immediately`);
      } else {
        // For regular battles, use animation
        object3D.scale.set(0.1, 0.1, 0.1);

        // Animate scale up using proper animation timing
        let startTime: number | null = null;
        const duration = 500; // ms

        const scaleUp = (timestamp: number) => {
          if (startTime === null) startTime = timestamp;
          const elapsed = timestamp - startTime;
          const progress = Math.min(elapsed / duration, 1);

          // Use easing function for smoother animation
          const scale = 0.1 + 0.9 * Math.sin(progress * Math.PI/2);
          object3D.scale.set(scale, scale, scale);

          if (progress < 1) {
            requestAnimationFrame(scaleUp);
          }
        };

        requestAnimationFrame(scaleUp);
      }

      // Add to scene
      activeScene.add(object3D);
      console.log(`ENTITY CREATION: Added entity ${entityId} to scene successfully`);

      // Store in participants map with the object3D reference
      this.battleParticipants.set(entityId, {
        id: entityId,
        object3D,
        color,
        petName: name,
        health: 100,
        maxHealth: 100,
        position: validPosition,
        rotation: validRotation
      });

      console.log(`ENTITY CREATION: Created entity ${entityId} (${name || 'unnamed'}) in battle scene at position:`, validPosition);

      // Add a text label with the entity name
      this.addEntityLabel(entityId, object3D, name);

      // Display a message in the battle log
      this.displayBattleLogEntry({
        type: 'system',
        message: `${name || 'Entity ' + entityId} has entered the battle!`,
        timestamp: Date.now()
      });

      // For test battles, create a backup entity with a different approach
      if (isTestBattle) {
        // Create a backup entity with a different geometry to ensure visibility
        setTimeout(() => {
          try {
            if (activeScene && this.battleParticipants.has(entityId)) {
              const entity = this.battleParticipants.get(entityId);
              if (!entity || !entity.object3D || !entity.object3D.parent) {
                console.log(`ENTITY CREATION: Creating backup entity for ${entityId} after delay`);

                // Create a more visible backup entity
                const backupGeometry = new THREE.BoxGeometry(2, 2, 2);
                const backupMaterial = new THREE.MeshBasicMaterial({
                  color: color,
                  wireframe: true,
                  transparent: true,
                  opacity: 0.8
                });

                const backupObject = new THREE.Mesh(backupGeometry, backupMaterial);
                backupObject.position.set(validPosition.x, validPosition.y, validPosition.z);
                backupObject.rotation.set(validRotation.x, validRotation.y, validRotation.z);

                // Add to scene
                activeScene.add(backupObject);

                // Update the entity in the participants map
                this.battleParticipants.set(entityId, {
                  id: entityId,
                  object3D: backupObject,
                  color,
                  petName: name,
                  health: 100,
                  maxHealth: 100,
                  position: validPosition,
                  rotation: validRotation
                });

                console.log(`ENTITY CREATION: Created backup entity for ${entityId}`);
              }
            }
          } catch (error) {
            console.error('ENTITY CREATION: Error creating backup entity:', error);
          }
        }, 500);
      }
    } catch (error) {
      console.error('ENTITY CREATION: Error creating entity:', error);
    }
  }

  /**
   * Add a text label to an entity
   * @param entityId The entity ID
   * @param object3D The 3D object to attach the label to
   * @param name Optional name to display
   */
  private addEntityLabel(entityId: string | number, object3D: THREE.Object3D, name?: string): void {
    // Get the active scene
    const activeScene = this.gameEngine.getScene();
    if (!activeScene) {
      console.error('No active scene found for entity label');
      return;
    }

    // Create a canvas for the text
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 64;

    const context = canvas.getContext('2d');
    if (!context) return;

    // Draw text on canvas
    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    context.font = 'bold 32px Arial';
    context.fillStyle = 'white';
    context.strokeStyle = 'black';
    context.lineWidth = 4;
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // Use provided name, or get from entity, or use default
    const displayName = name ||
                      (this.battleParticipants.get(entityId)?.petName) ||
                      `Pet ${entityId}`;

    // Draw text with outline for better visibility
    context.strokeText(displayName, canvas.width / 2, canvas.height / 2);
    context.fillText(displayName, canvas.width / 2, canvas.height / 2);

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    });

    // Create sprite
    const sprite = new THREE.Sprite(material);
    sprite.position.set(0, 2.5, 0); // Position above the entity
    sprite.scale.set(4, 1, 1);

    // Add sprite to entity
    object3D.add(sprite);
  }

  /**
   * Handle attack instruction
   * @param instruction The attack instruction
   */
  private handleAttackInstruction(instruction: RenderInstruction): void {
    const { attackerId, targetId, attackType } = instruction.data;

    // Find attacker and target
    const attacker = this.battleParticipants.get(attackerId);
    const target = this.battleParticipants.get(targetId);

    if (!attacker || !target) {
      console.warn('Attacker or target not found:', { attackerId, targetId });
      return;
    }

    // Create attack effect
    this.createAttackEffect(attacker, target, attackType);
  }

  /**
   * Create attack effect between two entities
   * @param attacker The attacking entity
   * @param target The target entity
   * @param attackType The type of attack
   */
  private createAttackEffect(attacker: any, target: any, attackType: string): void {
    // Get the active scene
    const activeScene = this.gameEngine.getScene();
    if (!activeScene || !attacker.object3D || !target.object3D) {
      console.error('No active scene found for attack effect or entities have no object3D');
      return;
    }

    // Get positions
    const attackerPosition = attacker.object3D.position.clone();
    const targetPosition = target.object3D.position.clone();

    // Create a projectile
    const geometry = new THREE.SphereGeometry(0.3, 8, 8);
    let material;

    // Set material based on attack type
    switch (attackType) {
      case 'fire':
        material = new THREE.MeshStandardMaterial({ color: 0xff0000, emissive: 0xff0000, emissiveIntensity: 0.5 });
        break;
      case 'ice':
        material = new THREE.MeshStandardMaterial({ color: 0x00ffff, emissive: 0x00ffff, emissiveIntensity: 0.5 });
        break;
      default:
        material = new THREE.MeshStandardMaterial({ color: 0xffffff });
    }

    const projectile = new THREE.Mesh(geometry, material);
    projectile.position.copy(attackerPosition);

    // Add to scene
    activeScene.add(projectile);

    // Store in effects map
    const effectId = `attack_${Date.now()}_${Math.random()}`;
    this.battleEffects.set(effectId, projectile);

    // Animate projectile
    const direction = targetPosition.clone().sub(attackerPosition).normalize();
    const distance = attackerPosition.distanceTo(targetPosition);
    const speed = 10; // units per second
    const duration = distance / speed;

    // Simple animation loop
    let elapsed = 0;
    const animate = (delta: number) => {
      elapsed += delta;
      const t = Math.min(elapsed / duration, 1);

      // Move projectile along path
      projectile.position.copy(attackerPosition).add(direction.clone().multiplyScalar(distance * t));

      if (t < 1) {
        // Continue animation
        requestAnimationFrame((timestamp) => animate(timestamp / 1000));
      } else {
        // Animation complete, remove projectile
        activeScene.remove(projectile);
        this.battleEffects.delete(effectId);

        // Create impact effect
        this.createImpactEffect(targetPosition, attackType);
      }
    };

    // Start animation
    requestAnimationFrame((timestamp) => animate(timestamp / 1000));
  }

  /**
   * Create impact effect at position
   * @param position The position for the impact
   * @param attackType The type of attack
   */
  private createImpactEffect(position: THREE.Vector3, attackType: string): void {
    // Get the active scene
    const activeScene = this.gameEngine.getScene();
    if (!activeScene) {
      console.error('No active scene found for impact effect');
      return;
    }

    // Create impact effect
    const geometry = new THREE.SphereGeometry(1, 16, 16);
    let material;

    // Set material based on attack type
    switch (attackType) {
      case 'fire':
        material = new THREE.MeshStandardMaterial({
          color: 0xff5500,
          emissive: 0xff5500,
          emissiveIntensity: 0.8,
          transparent: true,
          opacity: 0.8
        });
        break;
      case 'ice':
        material = new THREE.MeshStandardMaterial({
          color: 0x88ffff,
          emissive: 0x88ffff,
          emissiveIntensity: 0.8,
          transparent: true,
          opacity: 0.8
        });
        break;
      default:
        material = new THREE.MeshStandardMaterial({
          color: 0xffffff,
          transparent: true,
          opacity: 0.8
        });
    }

    const impact = new THREE.Mesh(geometry, material);
    impact.position.copy(position);
    impact.scale.set(0.1, 0.1, 0.1);

    // Add to scene
    activeScene.add(impact);

    // Store in effects map
    const effectId = `impact_${Date.now()}_${Math.random()}`;
    this.battleEffects.set(effectId, impact);

    // Animate impact
    let elapsed = 0;
    const duration = 0.5; // seconds

    const animate = (delta: number) => {
      elapsed += delta;
      const t = Math.min(elapsed / duration, 1);

      // Scale up then fade out
      const scale = 0.1 + 0.9 * Math.sin(t * Math.PI);
      impact.scale.set(scale, scale, scale);

      if (material.opacity !== undefined) {
        material.opacity = 0.8 * (1 - t);
      }

      if (t < 1) {
        // Continue animation
        requestAnimationFrame((timestamp) => animate(timestamp / 1000));
      } else {
        // Animation complete, remove impact
        activeScene.remove(impact);
        this.battleEffects.delete(effectId);
      }
    };

    // Start animation
    requestAnimationFrame((timestamp) => animate(timestamp / 1000));
  }

  /**
   * Handle use powerup instruction
   * @param instruction The use powerup instruction
   */
  private handleUsePowerupInstruction(instruction: RenderInstruction): void {
    const { entityId, powerupType } = instruction.data;

    // Find entity
    const entity = this.battleParticipants.get(entityId);
    if (!entity || !entity.object3D) {
      console.warn('Entity not found for powerup:', entityId);
      return;
    }

    // Create powerup effect
    this.createPowerupEffect(entity, powerupType);
  }

  /**
   * Create powerup effect for entity
   * @param entity The entity using the powerup
   * @param powerupType The type of powerup
   */
  private createPowerupEffect(entity: any, powerupType: PowerupType): void {
    // Get the active scene
    const activeScene = this.gameEngine.getScene();
    if (!activeScene || !entity.object3D) {
      console.error('No active scene found for powerup effect or entity has no object3D');
      return;
    }

    let color: number;
    let effectName: string;

    // Set effect properties based on powerup type
    switch (powerupType) {
      case PowerupType.FIRE:
        color = 0xff3300;
        effectName = 'fire';
        break;
      case PowerupType.ICE:
        color = 0x00ccff;
        effectName = 'ice';
        break;
      case PowerupType.SHIELD:
        color = 0xffcc00;
        effectName = 'shield';
        break;
      default:
        color = 0xffffff;
        effectName = 'unknown';
    }

    // Create effect mesh
    const geometry = new THREE.SphereGeometry(1.5, 16, 16);
    const material = new THREE.MeshStandardMaterial({
      color,
      emissive: color,
      emissiveIntensity: 0.5,
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    });

    const effect = new THREE.Mesh(geometry, material);
    effect.position.copy(entity.object3D.position);

    // Add to scene
    activeScene.add(effect);

    // Store in effects map with entity ID to track active effects
    const effectId = `powerup_${effectName}_${entity.id}`;

    // Remove existing effect of same type if any
    const existingEffect = this.battleEffects.get(effectId);
    if (existingEffect) {
      activeScene.remove(existingEffect);
    }

    this.battleEffects.set(effectId, effect);

    // For shield, keep the effect active
    if (powerupType === PowerupType.SHIELD) {
      // Shield effect stays until explicitly removed
      return;
    }

    // For other effects, animate and remove after duration
    let elapsed = 0;
    const duration = 3; // seconds

    const animate = (delta: number) => {
      elapsed += delta;
      const t = Math.min(elapsed / duration, 1);

      // Update effect position to follow entity
      effect.position.copy(entity.object3D.position);

      // Pulse effect
      const scale = 1 + 0.2 * Math.sin(elapsed * 5);
      effect.scale.set(scale, scale, scale);

      if (t < 1) {
        // Continue animation
        requestAnimationFrame((timestamp) => animate(timestamp / 1000));
      } else {
        // Animation complete, remove effect
        activeScene.remove(effect);
        this.battleEffects.delete(effectId);
      }
    };

    // Start animation
    requestAnimationFrame((timestamp) => animate(timestamp / 1000));
  }

  /**
   * Handle damage instruction
   * @param instruction The damage instruction
   */
  private handleDamageInstruction(instruction: RenderInstruction): void {
    const { entityId, damage, currentHealth, maxHealth } = instruction.data;

    // Find entity
    const entity = this.battleParticipants.get(entityId);
    if (!entity || !entity.object3D) {
      console.warn('Entity not found for damage:', entityId);
      return;
    }

    // Update entity health
    entity.health = currentHealth;
    entity.maxHealth = maxHealth;

    // Create damage effect
    this.createDamageEffect(entity, damage);
  }

  /**
   * Create damage effect for entity
   * @param entity The entity taking damage
   * @param damage The amount of damage
   */
  private createDamageEffect(entity: any, damage: number): void {
    // Get the active scene
    const activeScene = this.gameEngine.getScene();
    if (!activeScene || !entity.object3D) {
      console.error('No active scene found for damage effect or entity has no object3D');
      return;
    }

    // Create damage text
    const canvas = document.createElement('canvas');
    canvas.width = 128;
    canvas.height = 64;

    const context = canvas.getContext('2d');
    if (!context) return;

    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    context.font = 'bold 48px Arial';
    context.fillStyle = 'red';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(`-${damage}`, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    });

    const sprite = new THREE.Sprite(material);
    sprite.position.copy(entity.object3D.position);
    sprite.position.y += 2; // Position above entity
    sprite.scale.set(2, 1, 1);

    // Add to scene
    activeScene.add(sprite);

    // Store in effects map
    const effectId = `damage_${Date.now()}_${Math.random()}`;
    this.battleEffects.set(effectId, sprite);

    // Animate damage text
    let elapsed = 0;
    const duration = 1; // seconds

    const animate = (delta: number) => {
      elapsed += delta;
      const t = Math.min(elapsed / duration, 1);

      // Move upward and fade out
      sprite.position.y += delta * 1.5;
      sprite.material.opacity = 1 - t;

      if (t < 1) {
        // Continue animation
        requestAnimationFrame((timestamp) => animate(timestamp / 1000));
      } else {
        // Animation complete, remove sprite
        activeScene.remove(sprite);
        this.battleEffects.delete(effectId);
      }
    };

    // Start animation
    requestAnimationFrame((timestamp) => animate(timestamp / 1000));
  }

  /**
   * Handle victory instruction
   * @param instruction The victory instruction
   */
  private handleVictoryInstruction(instruction: RenderInstruction): void {
    const { winnerId, rewards } = instruction.data;

    // Find winner
    const winner = this.battleParticipants.get(winnerId);
    if (!winner) {
      console.warn('Winner not found:', winnerId);
      return;
    }

    // Create victory effect
    this.createVictoryEffect(winner);

    // Display victory message
    this.displayBattleLogEntry({
      type: 'victory',
      message: `${winner.name || 'Player ' + winnerId} wins the battle!`,
      timestamp: Date.now()
    });

    // Display rewards if any
    if (rewards) {
      this.displayBattleLogEntry({
        type: 'reward',
        message: `Rewards: ${rewards}`,
        timestamp: Date.now()
      });
    }
  }

  // This method is now consolidated with the createVictoryEffect method below
  private createVictoryParticlesLegacy(entity: any): void {
    // Get the active scene
    const activeScene = this.gameEngine.getScene();
    if (!activeScene || !entity.object3D) {
      console.error('No active scene found for victory effect or entity has no object3D');
      return;
    }

    // Create particles for victory effect
    const particleCount = 100;
    const particles = new THREE.Group();

    for (let i = 0; i < particleCount; i++) {
      const geometry = new THREE.SphereGeometry(0.1, 8, 8);
      const material = new THREE.MeshStandardMaterial({
        color: 0xffcc00,
        emissive: 0xffcc00,
        emissiveIntensity: 0.8
      });

      const particle = new THREE.Mesh(geometry, material);

      // Random position around entity
      const radius = 2;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particle.position.x = radius * Math.sin(phi) * Math.cos(theta);
      particle.position.y = radius * Math.sin(phi) * Math.sin(theta);
      particle.position.z = radius * Math.cos(phi);

      particles.add(particle);
    }

    // Position particles around entity
    particles.position.copy(entity.object3D.position);

    // Add to scene
    activeScene.add(particles);

    // Store in effects map
    const effectId = `victory_${Date.now()}`;
    this.battleEffects.set(effectId, particles);

    // Animate particles
    let elapsed = 0;
    const duration = 5; // seconds

    const animate = (delta: number) => {
      elapsed += delta;
      const t = Math.min(elapsed / duration, 1);

      // Rotate particles
      particles.rotation.y += delta * 0.5;

      // Expand particles outward
      particles.scale.set(1 + t, 1 + t, 1 + t);

      // Update each particle
      particles.children.forEach((particle, index) => {
        const speed = 0.2 + (index % 5) * 0.1;
        particle.position.normalize().multiplyScalar(2 + t * speed * 5);

        // Fade out particles gradually
        if (particle instanceof THREE.Mesh && particle.material instanceof THREE.MeshStandardMaterial) {
          particle.material.opacity = 1 - t;
          particle.material.transparent = true;
        }
      });

      if (t < 1) {
        // Continue animation
        requestAnimationFrame((timestamp) => animate(timestamp / 1000));
      } else {
        // Animation complete, remove particles
        activeScene.remove(particles);
        this.battleEffects.delete(effectId);
      }
    };

    // Start animation
    requestAnimationFrame((timestamp) => animate(timestamp / 1000));
  }

  /**
   * Create a victory effect for the winner
   * @param winner The winning participant
   */
  private createVictoryEffect(winner: any): void {
    if (!winner || !winner.object3D) return;

    console.log(`Creating victory effect for ${winner.petName || 'unnamed winner'}`);

    // Create a particle system for the victory effect
    const particleCount = 100;
    const particles = new THREE.Group();

    // Create particle geometry and material
    const particleGeometry = new THREE.SphereGeometry(0.2, 8, 8);
    const particleMaterial = new THREE.MeshBasicMaterial({
      color: winner.color || 0xffff00,
      transparent: true,
      opacity: 0.8
    });

    // Create particles with random positions around the winner
    for (let i = 0; i < particleCount; i++) {
      const particle = new THREE.Mesh(particleGeometry, particleMaterial.clone());

      // Set initial position at the winner's position
      particle.position.copy(winner.object3D.position);

      // Add random velocity for animation
      (particle as any).velocity = new THREE.Vector3(
        (Math.random() - 0.5) * 0.3,
        Math.random() * 0.5,
        (Math.random() - 0.5) * 0.3
      );

      // Add to particle group
      particles.add(particle);
    }

    // Add particles to scene
    const scene = this.gameEngine.getScene();
    scene?.add(particles);

    // Create animation for particles
    let startTime: number | null = null;
    const duration = 3000; // 3 seconds

    const animateParticles = (timestamp: number) => {
      if (startTime === null) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = elapsed / duration;

      // Update each particle position based on velocity and time
      particles.children.forEach(particle => {
        const p = particle as THREE.Mesh;
        const v = (p as any).velocity;

        // Update position
        p.position.x += v.x;
        p.position.y += v.y;
        p.position.z += v.z;

        // Add gravity effect
        v.y -= 0.01;

        // Fade out based on progress
        const material = p.material as THREE.MeshBasicMaterial;
        material.opacity = Math.max(0, 1 - progress);

        // Scale down
        const scale = Math.max(0.1, 1 - progress);
        p.scale.set(scale, scale, scale);
      });

      // Continue animation if not complete
      if (progress < 1) {
        requestAnimationFrame(animateParticles);
      } else {
        // Remove particles from scene when done
        const scene = this.gameEngine.getScene();
        scene?.remove(particles);

        // Dispose of geometries and materials
        particles.children.forEach(p => {
          const mesh = p as THREE.Mesh;
          mesh.geometry.dispose();
          (mesh.material as THREE.Material).dispose();
        });
      }
    };

    // Start animation
    requestAnimationFrame(animateParticles);

    // Also create a victory text above the winner
    this.createVictoryText(winner);
  }

  /**
   * Create victory text above the winner
   * @param winner The winning participant
   */
  private createVictoryText(winner: any): void {
    if (!winner || !winner.object3D) return;

    // Create a text sprite for "VICTORY!"
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return;

    // Set canvas size
    canvas.width = 256;
    canvas.height = 128;

    // Draw text
    context.fillStyle = '#000000';
    context.fillRect(0, 0, canvas.width, canvas.height);
    context.font = 'bold 48px Arial';
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // Create gradient
    const gradient = context.createLinearGradient(0, 0, canvas.width, 0);
    gradient.addColorStop(0, '#ff0000');
    gradient.addColorStop(0.5, '#ffff00');
    gradient.addColorStop(1, '#ff0000');

    context.fillStyle = gradient;
    context.fillText('VICTORY!', canvas.width / 2, canvas.height / 2);

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);

    // Create sprite material
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    });

    // Create sprite
    const sprite = new THREE.Sprite(material);
    sprite.scale.set(10, 5, 1);

    // Position above winner
    sprite.position.copy(winner.object3D.position);
    sprite.position.y += 5;

    // Add to scene
    const scene = this.gameEngine.getScene();
    scene?.add(sprite);

    // Animate the sprite
    let startTime: number | null = null;
    const duration = 5000; // 5 seconds

    const animateSprite = (timestamp: number) => {
      if (startTime === null) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = elapsed / duration;

      // Float up and down
      sprite.position.y = winner.object3D.position.y + 5 + Math.sin(progress * Math.PI * 4) * 0.5;

      // Rotate slightly
      sprite.rotation.z = Math.sin(progress * Math.PI * 2) * 0.1;

      // Fade out at the end
      if (progress > 0.7) {
        material.opacity = Math.max(0, 1 - ((progress - 0.7) / 0.3));
      }

      // Continue animation if not complete
      if (progress < 1) {
        requestAnimationFrame(animateSprite);
      } else {
        // Remove sprite from scene when done
        const scene = this.gameEngine.getScene();
        scene?.remove(sprite);

        // Dispose of texture and material
        texture.dispose();
        material.dispose();
      }
    };

    // Start animation
    requestAnimationFrame(animateSprite);
  }

  /**
   * Directly create entities from spawn instructions
   * This is a more aggressive approach for test battles
   * @param instructions The spawn instructions
   */
  private directlyCreateEntitiesFromInstructions(instructions: RenderInstruction[]): void {
    console.log('Directly creating entities from spawn instructions:', instructions);

    // Ensure we have a scene
    if (!this.gameEngine.getScene()) {
      console.error('No scene available for creating entities from instructions');
      return;
    }

    // Process each instruction
    instructions.forEach(instruction => {
      if (instruction.type !== RenderInstructionType.SPAWN_ENTITY || !instruction.data) {
        return;
      }

      const { entityId, position, rotation, name } = instruction.data;

      // Check if entity already exists in the scene
      const existingEntity = this.battleParticipants.get(entityId);
      if (!existingEntity || !existingEntity.object3D) {
        console.log(`Directly creating entity ${entityId} from instruction`);

        // Create entity in scene
        this.createEntityInScene(entityId, position, rotation, name);
      } else {
        console.log(`Entity ${entityId} already exists, updating properties`);

        // Update existing entity
        if (position && existingEntity.object3D) {
          existingEntity.object3D.position.set(
            position.x || 0,
            position.y || 1,
            position.z || 0
          );
          // Update stored position
          existingEntity.position = position;
        }

        if (rotation && existingEntity.object3D) {
          existingEntity.object3D.rotation.set(
            rotation.x || 0,
            rotation.y || 0,
            rotation.z || 0
          );
          // Update stored rotation
          existingEntity.rotation = rotation;
        }
      }
    });
  }

  /**
   * Verify that all entities exist and create them if they don't
   * @param instructions The spawn instructions
   */
  private verifyEntitiesExist(instructions: RenderInstruction[]): void {
    console.log('Verifying all entities exist');

    // Ensure we have a scene
    if (!this.gameEngine.getScene()) {
      console.error('No scene available for entity verification');
      return;
    }

    let missingEntities = 0;

    // Process each instruction
    instructions.forEach(instruction => {
      if (instruction.type !== RenderInstructionType.SPAWN_ENTITY || !instruction.data) {
        return;
      }

      const { entityId, position, rotation, name } = instruction.data;

      // Check if entity exists and is properly added to the scene
      const entity = this.battleParticipants.get(entityId);
      if (!entity || !entity.object3D || !entity.object3D.parent) {
        console.log(`Entity ${entityId} missing or not in scene, creating it`);
        missingEntities++;

        // Create entity in scene
        this.createEntityInScene(entityId, position, rotation, name);
      }
    });

    console.log(`Entity verification complete. Missing entities: ${missingEntities}`);
  }
}
