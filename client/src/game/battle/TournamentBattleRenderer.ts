import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import { RenderInstruction, RenderInstructionType } from '@shared/schema';
import { PetSpecter } from '../entities/PetSpecter.js';
import { audioManager } from '../audio/AudioManager.js';
import { ParticleSystem } from '../effects/ParticleSystem.js';

/**
 * <PERSON>les rendering instructions for tournament battles
 * This class processes rendering instructions from the server and updates the game scene accordingly
 */
export class TournamentBattleRenderer {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private activePets: Map<number, PetSpecter> = new Map();
  private particleSystem: ParticleSystem;
  private activeEffects: Map<string, { mesh: THREE.Mesh | THREE.Group, expireTime: number }> = new Map();

  constructor(scene: THREE.Scene, world: CANNON.World) {
    this.scene = scene;
    this.world = world;
    this.particleSystem = new ParticleSystem(scene);
  }

  /**
   * Process a batch of rendering instructions
   * @param instructions Array of rendering instructions from the server
   */
  public processInstructions(instructions: RenderInstruction[]): void {
    if (!instructions || instructions.length === 0) return;

    // Process each instruction
    for (const instruction of instructions) {
      this.processInstruction(instruction);
    }

    // Clean up expired effects
    this.cleanupExpiredEffects();
  }

  /**
   * Process a single rendering instruction
   * @param instruction The rendering instruction to process
   */
  private processInstruction(instruction: RenderInstruction): void {
    switch (instruction.type) {
      case RenderInstructionType.SpawnPet:
        this.handleSpawnPet(instruction);
        break;
      case RenderInstructionType.MovePet:
        this.handleMovePet(instruction);
        break;
      case RenderInstructionType.AttackAnimation:
        this.handleAttackAnimation(instruction);
        break;
      case RenderInstructionType.DefendAnimation:
        this.handleDefendAnimation(instruction);
        break;
      case RenderInstructionType.SpecialAnimation:
        this.handleSpecialAnimation(instruction);
        break;
      case RenderInstructionType.DamageEffect:
        this.handleDamageEffect(instruction);
        break;
      case RenderInstructionType.HealEffect:
        this.handleHealEffect(instruction);
        break;
      case RenderInstructionType.StatusEffect:
        this.handleStatusEffect(instruction);
        break;
      case RenderInstructionType.RemovePet:
        this.handleRemovePet(instruction);
        break;
      default:
        console.warn(`Unknown rendering instruction type: ${instruction.type}`);
    }
  }

  /**
   * Handle spawning a pet in the scene
   */
  private handleSpawnPet(instruction: RenderInstruction): void {
    if (!instruction.targetId || !instruction.petData) {
      console.error('Invalid spawn pet instruction: missing targetId or petData');
      return;
    }

    // Check if pet already exists
    if (this.activePets.has(instruction.targetId)) {
      console.warn(`Pet with ID ${instruction.targetId} already exists`);
      return;
    }

    // Create pet specter
    const petData = instruction.petData;
    const pet = new PetSpecter(
      String(petData.id),
      petData.petName,
      this.scene,
      this.world,
      petData.petSpecterId
    );

    // Set pet position and rotation
    if (instruction.position) {
      pet.setPosition(new THREE.Vector3(
        instruction.position.x,
        instruction.position.y,
        instruction.position.z
      ));
    }

    if (instruction.rotation) {
      const mesh = pet.getMesh();
      if (mesh) {
        mesh.rotation.set(
          instruction.rotation.x,
          instruction.rotation.y,
          instruction.rotation.z
        );
      }
    }

    // Set pet health
    pet.health = petData.health;
    pet.maxHealth = petData.maxHealth;

    // Add to active pets
    this.activePets.set(instruction.targetId, pet);

    // Play spawn sound
    audioManager.playSound('specterSpawn');

    // Create spawn effect
    this.particleSystem.createEffect(
      'spawn',
      pet.getPosition(),
      { color: 0x00ffff, count: 30, duration: 1000 }
    );

    console.log(`Spawned pet ${petData.petName} (ID: ${instruction.targetId})`);
  }

  /**
   * Handle moving a pet in the scene
   */
  private handleMovePet(instruction: RenderInstruction): void {
    if (!instruction.targetId || !instruction.position) {
      console.error('Invalid move pet instruction: missing targetId or position');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Set pet position
    pet.setPosition(new THREE.Vector3(
      instruction.position.x,
      instruction.position.y,
      instruction.position.z
    ));

    // Set pet rotation if provided
    if (instruction.rotation) {
      const mesh = pet.getMesh();
      if (mesh) {
        mesh.rotation.set(
          instruction.rotation.x,
          instruction.rotation.y,
          instruction.rotation.z
        );
      }
    }
  }

  /**
   * Handle attack animation
   */
  private handleAttackAnimation(instruction: RenderInstruction): void {
    if (!instruction.sourceId || !instruction.targetId) {
      console.error('Invalid attack animation instruction: missing sourceId or targetId');
      return;
    }

    // Get source and target pets
    const sourcePet = this.activePets.get(instruction.sourceId);
    const targetPet = this.activePets.get(instruction.targetId);

    if (!sourcePet || !targetPet) {
      console.warn(`Source or target pet not found for attack animation`);
      return;
    }

    // Get positions
    const sourcePos = sourcePet.getPosition();
    const targetPos = targetPet.getPosition();

    // Create attack effect
    this.particleSystem.createEffect(
      'attack',
      sourcePos,
      {
        color: 0xff0000,
        count: 15,
        duration: 500,
        direction: new THREE.Vector3().subVectors(targetPos, sourcePos).normalize()
      }
    );

    // Play attack sound
    audioManager.playSound('attack');

    // Make source pet face target
    const direction = new THREE.Vector3().subVectors(targetPos, sourcePos);
    const angle = Math.atan2(direction.x, direction.z);

    // Set rotation on the mesh directly since PetSpecter doesn't have setRotation
    const sourceMesh = sourcePet.getMesh();
    if (sourceMesh) {
      sourceMesh.rotation.y = angle;
    }

    // Animate source pet (simple scale animation)
    const mesh = sourcePet.getMesh();
    if (mesh) {
      // Save original scale
      const originalScale = mesh.scale.clone();

      // Scale up quickly
      mesh.scale.multiplyScalar(1.2);

      // Return to original scale after a short delay
      setTimeout(() => {
        if (mesh) {
          mesh.scale.copy(originalScale);
        }
      }, 200);
    }
  }

  /**
   * Handle defend animation
   */
  private handleDefendAnimation(instruction: RenderInstruction): void {
    if (!instruction.targetId) {
      console.error('Invalid defend animation instruction: missing targetId');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Get position
    const position = pet.getPosition();

    // Create shield effect
    const shieldGeometry = new THREE.SphereGeometry(2, 16, 16);
    const shieldMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 0.3,
      wireframe: true
    });

    const shield = new THREE.Mesh(shieldGeometry, shieldMaterial);
    shield.position.copy(position);
    this.scene.add(shield);

    // Store in active effects
    const effectId = `shield_${instruction.targetId}_${Date.now()}`;
    const duration = instruction.duration || 3000;
    this.activeEffects.set(effectId, {
      mesh: shield,
      expireTime: Date.now() + duration
    });

    // Play shield sound
    audioManager.playSound('shield');
  }

  /**
   * Handle special attack animation
   */
  private handleSpecialAnimation(instruction: RenderInstruction): void {
    if (!instruction.targetId) {
      console.error('Invalid special animation instruction: missing targetId');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Get position
    const position = pet.getPosition();

    // Handle different special effect types
    const effectType = instruction.effectType || 'special_attack';

    switch (effectType) {
      case 'special_attack':
        // Create special attack effect
        this.particleSystem.createEffect(
          'special',
          position,
          {
            color: 0xffaa00,
            count: 30,
            duration: 1000,
            radius: 3
          }
        );

        // Play special attack sound
        audioManager.playSound('specialAttack');
        break;

      case 'victory':
        // Create victory effect
        this.particleSystem.createEffect(
          'victory',
          position,
          {
            color: 0xffff00,
            count: 50,
            duration: 2000,
            radius: 4
          }
        );

        // Play victory sound
        audioManager.playSound('victory');
        break;

      default:
        console.warn(`Unknown special effect type: ${effectType}`);
    }
  }

  /**
   * Handle damage effect
   */
  private handleDamageEffect(instruction: RenderInstruction): void {
    if (!instruction.targetId) {
      console.error('Invalid damage effect instruction: missing targetId');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Get position
    const position = instruction.position || pet.getPosition();

    // Create damage effect
    this.particleSystem.createEffect(
      'damage',
      position,
      {
        color: 0xff0000,
        count: 20,
        duration: 500
      }
    );

    // Play damage sound
    audioManager.playSound('damage');

    // Show damage number if provided
    if (instruction.value) {
      this.createDamageNumber(position, instruction.value);
    }

    // Update pet health if needed
    if (pet.health > 0) {
      // Flash the pet red
      const mesh = pet.getMesh();
      if (mesh) {
        // Handle material flashing for Groups by traversing children
        const originalMaterials = new Map<THREE.Object3D, THREE.Material | THREE.Material[]>();

        mesh.traverse((child) => {
          if (child instanceof THREE.Mesh && child.material) {
            // Save original material
            originalMaterials.set(child, child.material);

            // Apply damage material
            const damageMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
            if (Array.isArray(child.material)) {
              child.material = Array(child.material.length).fill(damageMaterial);
            } else {
              child.material = damageMaterial;
            }
          }
        });

        // Restore original materials after a short delay
        setTimeout(() => {
          originalMaterials.forEach((originalMaterial, child) => {
            if (child instanceof THREE.Mesh) {
              child.material = originalMaterial;
            }
          });
        }, 200);
      }
    }
  }

  /**
   * Handle heal effect
   */
  private handleHealEffect(instruction: RenderInstruction): void {
    if (!instruction.targetId) {
      console.error('Invalid heal effect instruction: missing targetId');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Get position
    const position = pet.getPosition();

    // Create heal effect
    this.particleSystem.createEffect(
      'heal',
      position,
      {
        color: 0x00ff00,
        count: 20,
        duration: 1000
      }
    );

    // Play heal sound
    audioManager.playSound('heal');

    // Show heal number if provided
    if (instruction.value) {
      this.createHealNumber(position, instruction.value);
    }
  }

  /**
   * Handle status effect
   */
  private handleStatusEffect(instruction: RenderInstruction): void {
    if (!instruction.targetId || !instruction.effectType) {
      console.error('Invalid status effect instruction: missing targetId or effectType');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Get position
    const position = pet.getPosition();

    // Handle different status effect types
    switch (instruction.effectType) {
      case 'shield_expire':
        // Remove shield effect
        for (const [effectId, effect] of this.activeEffects.entries()) {
          if (effectId.startsWith(`shield_${instruction.targetId}_`)) {
            this.scene.remove(effect.mesh);
            this.activeEffects.delete(effectId);
          }
        }
        break;

      default:
        console.warn(`Unknown status effect type: ${instruction.effectType}`);
    }
  }

  /**
   * Handle removing a pet from the scene
   */
  private handleRemovePet(instruction: RenderInstruction): void {
    if (!instruction.targetId) {
      console.error('Invalid remove pet instruction: missing targetId');
      return;
    }

    // Get pet
    const pet = this.activePets.get(instruction.targetId);
    if (!pet) {
      console.warn(`Pet with ID ${instruction.targetId} not found`);
      return;
    }

    // Get position for effect
    const position = pet.getPosition();

    // Create remove effect
    this.particleSystem.createEffect(
      'remove',
      position,
      {
        color: 0x888888,
        count: 30,
        duration: 1000
      }
    );

    // Play remove sound
    audioManager.playSound('specterCapture');

    // Remove pet
    pet.dispose();
    this.activePets.delete(instruction.targetId);
  }

  /**
   * Create a floating damage number
   */
  private createDamageNumber(position: THREE.Vector3, value: number): void {
    // Create text sprite
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return;

    canvas.width = 128;
    canvas.height = 64;

    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    context.font = 'bold 48px Arial';
    context.fillStyle = '#ff0000';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(`-${value}`, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    });

    const sprite = new THREE.Sprite(material);
    sprite.position.copy(position);
    sprite.position.y += 2; // Position above the pet
    sprite.scale.set(2, 1, 1);

    this.scene.add(sprite);

    // Animate the sprite
    const startTime = Date.now();
    const duration = 1000;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      if (elapsed < duration) {
        // Move upward
        sprite.position.y += 0.01;

        // Fade out
        const opacity = 1 - (elapsed / duration);
        sprite.material.opacity = opacity;

        requestAnimationFrame(animate);
      } else {
        // Remove sprite
        this.scene.remove(sprite);
        sprite.material.dispose();
        texture.dispose();
      }
    };

    animate();
  }

  /**
   * Create a floating heal number
   */
  private createHealNumber(position: THREE.Vector3, value: number): void {
    // Create text sprite
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return;

    canvas.width = 128;
    canvas.height = 64;

    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    context.font = 'bold 48px Arial';
    context.fillStyle = '#00ff00';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(`+${value}`, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    });

    const sprite = new THREE.Sprite(material);
    sprite.position.copy(position);
    sprite.position.y += 2; // Position above the pet
    sprite.scale.set(2, 1, 1);

    this.scene.add(sprite);

    // Animate the sprite
    const startTime = Date.now();
    const duration = 1000;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      if (elapsed < duration) {
        // Move upward
        sprite.position.y += 0.01;

        // Fade out
        const opacity = 1 - (elapsed / duration);
        sprite.material.opacity = opacity;

        requestAnimationFrame(animate);
      } else {
        // Remove sprite
        this.scene.remove(sprite);
        sprite.material.dispose();
        texture.dispose();
      }
    };

    animate();
  }

  /**
   * Clean up expired effects
   */
  private cleanupExpiredEffects(): void {
    const now = Date.now();

    for (const [effectId, effect] of this.activeEffects.entries()) {
      if (effect.expireTime <= now) {
        this.scene.remove(effect.mesh);
        this.activeEffects.delete(effectId);
      }
    }
  }

  /**
   * Clear all pets and effects
   */
  public clear(): void {
    // Remove all pets
    for (const pet of this.activePets.values()) {
      pet.dispose();
    }
    this.activePets.clear();

    // Remove all effects
    for (const effect of this.activeEffects.values()) {
      this.scene.remove(effect.mesh);
    }
    this.activeEffects.clear();
  }
}
