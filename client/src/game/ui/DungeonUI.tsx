import React, { useState, useEffect } from 'react';
import { DungeonDifficulty } from '../world/DungeonGenerator.js';

interface DungeonUIProps {
  isInDungeon: boolean;
  dungeonLevel: number;
  dungeonDifficulty: DungeonDifficulty;
  roomsCleared: number;
  totalRooms: number;
  bossDefeated: boolean;
  enemiesDefeated: number;
  lootCollected: number;
}

const DungeonUI: React.FC<DungeonUIProps> = ({
  isInDungeon,
  dungeonLevel,
  dungeonDifficulty,
  roomsCleared,
  totalRooms,
  bossDefeated,
  enemiesDefeated,
  lootCollected
}) => {
  const [showUI, setShowUI] = useState(false);
  
  // Show UI with a slight delay when entering dungeon
  useEffect(() => {
    if (isInDungeon) {
      const timer = setTimeout(() => {
        setShowUI(true);
      }, 500);
      
      return () => clearTimeout(timer);
    } else {
      setShowUI(false);
    }
  }, [isInDungeon]);
  
  if (!showUI) return null;
  
  // Get difficulty color
  const getDifficultyColor = () => {
    switch (dungeonDifficulty) {
      case DungeonDifficulty.EASY:
        return 'text-green-400';
      case DungeonDifficulty.MEDIUM:
        return 'text-yellow-400';
      case DungeonDifficulty.HARD:
        return 'text-orange-400';
      case DungeonDifficulty.NIGHTMARE:
        return 'text-red-400';
      default:
        return 'text-white';
    }
  };
  
  return (
    <div className="fixed top-4 right-4 bg-black/70 p-3 rounded-lg border border-gray-700 text-white font-mono text-sm z-50">
      <div className="mb-2 text-center font-bold border-b border-gray-700 pb-1">
        <span>DUNGEON LEVEL {dungeonLevel}</span>
        <span className={`ml-2 ${getDifficultyColor()}`}>
          {dungeonDifficulty.toUpperCase()}
        </span>
      </div>
      
      <div className="grid grid-cols-2 gap-x-4 gap-y-1">
        <div>Rooms:</div>
        <div>{roomsCleared} / {totalRooms}</div>
        
        <div>Boss:</div>
        <div>{bossDefeated ? '✓ DEFEATED' : '✗ ALIVE'}</div>
        
        <div>Enemies:</div>
        <div>{enemiesDefeated} defeated</div>
        
        <div>Loot:</div>
        <div>{lootCollected} collected</div>
      </div>
      
      {!bossDefeated && (
        <div className="mt-2 text-center text-xs text-yellow-300">
          Defeat the boss to unlock the exit!
        </div>
      )}
    </div>
  );
};

export default DungeonUI;
