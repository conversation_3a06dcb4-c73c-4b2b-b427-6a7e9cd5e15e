import React from 'react';
import { SpecterType } from '../types.js';
import { PLAYER_MAX_HEALTH, PLAYER_JETPACK_MAX_FUEL } from '../constants.js';
import { GameMode, PlayerState } from '@shared/schema.js';

interface TeamInfo {
  id: number;
  name: string;
  color: string;
  score: number;
}

interface HUDProps {
  score: number;
  ammoType: string;
  ammo: number[];
  spectersCaptured: number;
  health?: number;
  jetpackFuel?: number;
  // Multiplayer props
  isMultiplayer?: boolean;
  gameMode?: string;
  playerName?: string;
  isHost?: boolean;
  players?: PlayerState[];
  teams?: TeamInfo[];
  currentLevel?: number;
  // Add homingActive prop
  homingActive?: boolean;
  // Add localPlayerId prop
  localPlayerId?: string | null;
}

const HUD: React.FC<HUDProps> = ({
  score,
  ammoType,
  ammo,
  spectersCaptured,
  health = PLAYER_MAX_HEALTH,
  jetpackFuel = PLAYER_JETPACK_MAX_FUEL,
  // Multiplayer props with defaults
  isMultiplayer = false,
  gameMode = 'single',
  playerName = 'Player',
  isHost = false,
  players = [],
  teams = [],
  currentLevel = 1,
  // Homing missile props with defaults
  homingActive = false,
  // Add localPlayerId with default null
  localPlayerId = null
}) => {
  // Debug log to check homingActive prop
  // console.log("HUD RENDER - Homing active:", homingActive);

  // Map ammo types to colors
  const ammoColors = {
    'Gravity Well': 'bg-cyan-500',
    'Time Bubble': 'bg-fuchsia-500',
    'Phase Net': 'bg-yellow-500'
  };

  // Get color for current ammo type
  const currentColor = ammoColors[ammoType as keyof typeof ammoColors] || 'bg-blue-500';

  // Calculate health percentage for display
  const healthPercentage = Math.max(0, Math.min(100, (health / PLAYER_MAX_HEALTH) * 100));

  // Calculate jetpack fuel percentage for display
  const fuelPercentage = Math.max(0, Math.min(100, (jetpackFuel / PLAYER_JETPACK_MAX_FUEL) * 100));

  // Determine health bar color
  const healthBarColor =
    healthPercentage > 60 ? 'bg-green-500' :
    healthPercentage > 30 ? 'bg-yellow-500' :
    'bg-red-500';

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Top HUD - Score and Level Info */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
        <div className="text-purple-400 text-xs mb-1 tracking-wider">
          {isMultiplayer ? `${gameMode.toUpperCase()} MODE - ` : ''}LEVEL {currentLevel}
        </div>

        <div className="text-yellow-300 title-font font-semibold text-2xl tracking-wider">
          {score.toLocaleString()}
        </div>

        <div className="text-blue-300 text-xs tracking-wider">
          SPECTERS CAPTURED: {spectersCaptured}
        </div>

        {isMultiplayer && isHost && (
          <div className="mt-2 bg-purple-900 bg-opacity-60 px-3 py-1 rounded-full text-xs text-white border border-purple-500">
            HOST
          </div>
        )}
      </div>

      {/* HOMING INDICATOR - ALWAYS VISIBLE WHEN ACTIVE */}
      {homingActive && (
        <div style={{
          position: 'fixed',
          top: '140px',
          left: '50%',
          transform: 'translateX(-50%)',
          backgroundColor: 'rgba(255, 0, 0, 0.9)',
          color: 'white',
          padding: '15px 25px',
          borderRadius: '8px',
          fontWeight: 'bold',
          fontSize: '24px',
          zIndex: 10000,
          border: '4px solid yellow',
          boxShadow: '0 0 30px rgba(255, 0, 0, 0.9)',
          animation: 'blink 0.5s alternate infinite',
          textShadow: '2px 2px 0 black',
          pointerEvents: 'none'
        }}>
          ⚠️ HOMING ACTIVE ⚠️
        </div>
      )}

      {/* Additional backup homing indicator that will appear elsewhere on screen */}
      {homingActive && (
        <div style={{
          position: 'fixed',
          bottom: '100px',
          right: '20px',
          backgroundColor: 'red',
          color: 'white',
          padding: '10px 15px',
          borderRadius: '8px',
          fontWeight: 'bold',
          fontSize: '18px',
          zIndex: 10000,
          border: '2px solid yellow',
          boxShadow: '0 0 15px rgba(255, 0, 0, 0.8)',
          animation: 'pulse 0.7s alternate infinite',
          pointerEvents: 'none'
        }}>
          HOMING ACTIVE
        </div>
      )}

      {/* Emergency animation for homing indicator */}
      <style>
        {`
          @keyframes blink {
            from { opacity: 0.7; }
            to { opacity: 1; }
          }
          @keyframes pulse {
            from { transform: scale(0.95); }
            to { transform: scale(1.05); }
          }
        `}
      </style>

      {/* Right side - Multiplayer info */}
      {isMultiplayer && (
        <div className="absolute top-6 right-6 w-48 opacity-90">
          {/* Players list */}
          <div className="bg-gray-900 bg-opacity-70 border border-blue-900 rounded-md p-2 mb-3">
            <div className="text-blue-400 text-xs font-semibold mb-2 flex justify-between items-center">
              <span>PLAYERS ({players.length})</span>
              {gameMode === GameMode.PvP && <span>SCORE</span>}
            </div>

            <div className="space-y-1 max-h-40 overflow-y-auto pr-1">
              {/* Map over PlayerState array */}
              {players.map(player => (
                <div key={player.id} className="flex justify-between items-center text-xs">
                  <div className="flex items-center">
                    {/* Check if player.id matches local player ID */}
                    <span className={localPlayerId === player.id ? 'text-green-400' : 'text-white'}>
                      {/* Use player.name from PlayerState */}
                      {player.name || 'Unnamed Player'}
                      {localPlayerId === player.id ? ' (You)' : ''}
                    </span>
                  </div>

                  {gameMode === GameMode.PvP && (
                    <span className="text-blue-400">
                      {/* Use player.score (assuming it exists in PlayerState or can be added) */}
                      {player.score || 0}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Teams (if in team mode) */}
          {gameMode === GameMode.PvP && teams.length > 0 && (
            <div className="bg-gray-900 bg-opacity-70 border border-blue-900 rounded-md p-2">
              <div className="text-blue-400 text-xs font-semibold mb-2">
                TEAMS
              </div>

              <div className="space-y-2">
                {teams.map(team => (
                  <div key={team.id} className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-1"
                        style={{ backgroundColor: team.color }}
                      ></div>
                      <span className="text-xs text-white">{team.name}</span>
                    </div>
                    <span className="text-xs text-blue-400">{team.score}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Bottom HUD - Rifle status with Weapon Display */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
        {/* Weapon image */}
        <div className="mb-2 relative">
          <div className="w-32 h-12 flex items-center justify-center">
            <div className={`h-2 w-20 ${currentColor} rounded-full mb-1 opacity-70`}></div>
            <div className={`absolute w-6 h-6 rounded-full ${currentColor} ${homingActive ? 'animate-pulse' : ''} opacity-80 top-1`}></div>
            <div className="absolute w-24 h-1.5 bg-gray-700 bottom-0 rounded-sm"></div>
          </div>
          <div className="text-white title-font text-sm mb-1 tracking-wider text-center">
            {ammoType}
          </div>
        </div>

        {/* Ammo indicators - current selected type larger and in foreground */}
        <div className="flex flex-col space-y-1 items-center">
          {/* Current ammo selection with larger indicators */}
          <div className="flex space-x-1.5 mb-1">
            {ammoType === 'Phase Net' && (
              <div className="flex items-center">
                <div className="text-yellow-500 text-xl font-bold">∞</div>
                <div className="ml-1 w-4 h-4 rounded-full bg-yellow-500"></div>
              </div>
            )}
            {ammoType === 'Time Bubble' && Array.from({ length: 5 }).map((_, i) => (
              <div key={`curr-${i}`} className={`w-4 h-4 rounded-full ${i < ammo[1] ? 'bg-fuchsia-500' : 'bg-gray-700 border border-fuchsia-900'}`}></div>
            ))}
            {ammoType === 'Gravity Well' && Array.from({ length: 5 }).map((_, i) => (
              <div key={`curr-${i}`} className={`w-4 h-4 rounded-full ${i < ammo[2] ? 'bg-cyan-500' : 'bg-gray-700 border border-cyan-900'}`}></div>
            ))}
          </div>

          {/* Other ammo types in background - smaller and more subtle */}
          <div className="flex justify-between w-full">
            {/* Phase Net */}
            {ammoType !== 'Phase Net' && (
              <div className="flex space-x-0.5 opacity-60">
                <div className="w-3 h-3 rounded-full bg-yellow-800 flex items-center justify-center text-[8px] text-white">1</div>
                <div className="text-yellow-500 text-xs font-bold ml-0.5">∞</div>
              </div>
            )}

            {/* Time Bubble */}
            {ammoType !== 'Time Bubble' && (
              <div className="flex space-x-0.5 opacity-60">
                <div className="w-3 h-3 rounded-full bg-fuchsia-800 flex items-center justify-center text-[8px] text-white">2</div>
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={`t-${i}`} className={`w-2 h-2 rounded-full ${i < ammo[1] ? 'bg-fuchsia-500' : 'bg-gray-700'}`}></div>
                ))}
              </div>
            )}

            {/* Gravity Well */}
            {ammoType !== 'Gravity Well' && (
              <div className="flex space-x-0.5 opacity-60">
                <div className="w-3 h-3 rounded-full bg-cyan-800 flex items-center justify-center text-[8px] text-white">3</div>
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={`g-${i}`} className={`w-2 h-2 rounded-full ${i < ammo[2] ? 'bg-cyan-500' : 'bg-gray-700'}`}></div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bottom right - Ammo type keys */}
      <div className="absolute bottom-6 right-6 flex flex-col items-end space-y-2">
        <div className={`flex items-center space-x-2 ${ammoType === 'Phase Net' ? 'opacity-100' : 'opacity-50'}`}>
          <div className="text-white text-sm">Phase Net</div>
          <div className="w-6 h-6 flex items-center justify-center border border-yellow-500 text-yellow-500 text-xs">1</div>
        </div>

        <div className={`flex items-center space-x-2 ${ammoType === 'Time Bubble' ? 'opacity-100' : 'opacity-50'}`}>
          <div className="text-white text-sm">Time Bubble</div>
          <div className="w-6 h-6 flex items-center justify-center border border-fuchsia-500 text-fuchsia-500 text-xs">2</div>
        </div>

        <div className={`flex items-center space-x-2 ${ammoType === 'Gravity Well' ? 'opacity-100' : 'opacity-50'}`}>
          <div className="text-white text-sm">Gravity Well</div>
          <div className="w-6 h-6 flex items-center justify-center border border-cyan-500 text-cyan-500 text-xs">3</div>
        </div>
      </div>

      {/* Left side - Player status */}
      <div className="absolute top-6 left-6 flex flex-col space-y-4 w-48">
        {/* Health bar */}
        <div className="flex flex-col">
          <div className="flex justify-between items-center mb-1">
            <span className="text-white text-xs">HEALTH</span>
            <span className="text-white text-xs">{Math.ceil(health)}/{PLAYER_MAX_HEALTH}</span>
          </div>
          <div className="h-3 bg-gray-800 rounded-full overflow-hidden">
            <div
              className={`h-full ${healthBarColor} transition-all duration-300`}
              style={{ width: `${healthPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Jetpack Fuel */}
        <div className="flex flex-col">
          <div className="flex justify-between items-center mb-1">
            <span className="text-white text-xs">JETPACK</span>
            <span className="text-white text-xs">{Math.ceil(jetpackFuel)}/{PLAYER_JETPACK_MAX_FUEL}</span>
          </div>
          <div className="h-3 bg-gray-800 rounded-full overflow-hidden">
            <div
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${fuelPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Control reminders */}
        <div className="bg-gray-900 bg-opacity-70 p-2 rounded-md border border-gray-800 text-xs text-white">
          <div className="mb-1"><span className="text-gray-400">SHIFT</span> key to activate jetpack</div>
          <div><span className="text-gray-400">G</span> key to fire grappling hook</div>
          <div><span className="text-gray-400">1, 2, and 3</span> Keys to switch weapons</div>
          <div><span className="text-gray-400">-/+</span> Keys to switch songs</div>
          <div><span className="text-gray-400">P</span> key for Pet Specter Menu</div>
          <div><span className="text-gray-400">E</span> key to enter at MerchGenie Kiosk (Blue Tower)</div>
        </div>
      </div>
    </div>
  );
};

export default HUD;