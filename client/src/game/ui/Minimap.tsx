import React, { useEffect, useRef } from 'react';
import { PlayerInfo, TeamInfo } from '../types.js';

interface MinimapProps {
  playerPosition: { x: number; y: number; z: number };
  specters: { 
    id: number; 
    position: { x: number; y: number; z: number }; 
    color: string;
    isPlayer?: boolean; 
    name?: string;
  }[];
  players?: PlayerInfo[];
  teams?: TeamInfo[];
  isMultiplayer?: boolean;
}

const Minimap: React.FC<MinimapProps> = ({ 
  playerPosition,
  specters,
  players = [],
  teams = [],
  isMultiplayer = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Scale and render entities on the minimap
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background - dark blue color
    ctx.fillStyle = 'rgba(10, 16, 32, 0.85)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid for reference
    ctx.strokeStyle = 'rgba(72, 84, 120, 0.3)';
    ctx.lineWidth = 1;
    
    // Draw grid lines
    const gridSize = 10;
    const gridStep = canvas.width / gridSize;
    
    for (let i = 0; i <= gridSize; i++) {
      // Vertical lines
      ctx.beginPath();
      ctx.moveTo(i * gridStep, 0);
      ctx.lineTo(i * gridStep, canvas.height);
      ctx.stroke();
      
      // Horizontal lines
      ctx.beginPath();
      ctx.moveTo(0, i * gridStep);
      ctx.lineTo(canvas.width, i * gridStep);
      ctx.stroke();
    }

    // Draw map center
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    ctx.strokeStyle = 'rgba(140, 150, 230, 0.5)';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
    ctx.stroke();

    // Scale factor for converting world coordinates to minimap coordinates
    const scale = 2.5; // Adjust to make minimap show enough area
    
    // Draw entities - both specters and other players
    specters.forEach(entity => {
      // Calculate relative position to player
      const relX = entity.position.x - playerPosition.x;
      const relZ = entity.position.z - playerPosition.z;
      
      // Convert to minimap coordinates
      const mapX = centerX + relX / scale;
      const mapY = centerY + relZ / scale;

      if (entity.isPlayer) {
        // Draw other player as triangle
        const playerColor = entity.color || '#55AAFF';
        
        // Draw player triangle (pointing in direction of movement)
        ctx.fillStyle = playerColor;
        ctx.beginPath();
        ctx.moveTo(mapX, mapY - 5); // Top point
        ctx.lineTo(mapX - 4, mapY + 3); // Bottom left
        ctx.lineTo(mapX + 4, mapY + 3); // Bottom right
        ctx.closePath();
        ctx.fill();
        
        // Optional: Draw name label for other players
        if (entity.name) {
          ctx.font = '8px Arial';
          ctx.fillStyle = 'white';
          ctx.textAlign = 'center';
          ctx.fillText(entity.name, mapX, mapY + 12);
        }
      } else {
        // Draw specter dot (use their color)
        ctx.fillStyle = entity.color || '#FF5555';
        ctx.beginPath();
        ctx.arc(mapX, mapY, 4, 0, Math.PI * 2);
        ctx.fill();
        
        // Add pulse effect for better visibility
        ctx.strokeStyle = entity.color || '#FF5555';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(mapX, mapY, 6, 0, Math.PI * 2);
        ctx.stroke();
      }
    });
    
    // Draw player (always in center)
    ctx.fillStyle = '#00FF00'; // Green for player
    ctx.beginPath();
    ctx.arc(centerX, centerY, 4, 0, Math.PI * 2);
    ctx.fill();
    
    // Draw direction indicator (triangle pointing in direction of camera)
    ctx.fillStyle = '#FFFFFF';
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - 8); // Top point (forward)
    ctx.lineTo(centerX - 4, centerY - 2); // Bottom left
    ctx.lineTo(centerX + 4, centerY - 2); // Bottom right
    ctx.closePath();
    ctx.fill();
  }, [playerPosition, specters, players, teams, isMultiplayer]);

  return (
    <div className="flex flex-col items-center">
      <div 
        ref={containerRef} 
        className="w-32 h-32 rounded-full overflow-hidden border-2 border-blue-900"
        style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
      >
        <canvas 
          ref={canvasRef}
          width={128}
          height={128}
        />
      </div>
      <div className="text-center mt-1 text-xs text-blue-400 font-semibold">
        RADAR
      </div>
    </div>
  );
};

export default Minimap;