import * as THREE from 'three';
import * as CA<PERSON><PERSON><PERSON> from 'cannon-es';
import { DungeonDifficulty } from '../world/DungeonGenerator';
import { SeededRandom } from '../utils/SeededRandom';
import { audioManager as AudioManager } from '../audio/AudioManager';

// Boss types for dungeon
export enum DungeonBossType {
  SHADOW_KING = 'shadow_king',
  SPECTRAL_GUARDIAN = 'spectral_guardian',
  VOID_HARBINGER = 'void_harbinger',
  ECTOPLASMIC_TITAN = 'ectoplasmic_titan',
  NIGHTMARE_WEAVER = 'nightmare_weaver'
}

/**
 * DungeonBoss class for boss enemies in the dungeon
 */
export class DungeonBoss {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private position: THREE.Vector3;
  private mesh: THREE.Group;
  private body: CANNON.Body;
  private type: DungeonBossType;
  private difficulty: DungeonDifficulty;
  private dungeonLevel: number;
  private random: SeededRandom;
  private health: number = 100;
  private maxHealth: number = 100;
  private attackPower: number = 10;
  private speed: number = 3;
  private attackRange: number = 5; // Larger range than regular enemies
  private attackCooldown: number = 0;
  private specialAttackCooldown: number = 0;
  private _isDefeated: boolean = false;
  private isAttacking: boolean = false;
  private isSpecialAttacking: boolean = false;
  private detectionRange: number = 20; // Larger detection range
  private pointValue: number = 100;
  private attackPhase: number = 0; // For multi-phase boss fights
  private healthThresholds: number[] = [0.7, 0.4, 0.1]; // Thresholds for phase changes

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    difficulty: DungeonDifficulty,
    dungeonLevel: number,
    seed: number
  ) {
    this.scene = scene;
    this.world = world;
    this.position = position;
    this.difficulty = difficulty;
    this.dungeonLevel = dungeonLevel;
    this.random = new SeededRandom(seed);

    // Determine boss type based on dungeon level
    this.type = this.determineBossType();

    // Set base stats based on type and difficulty
    this.setBaseStats();

    // Create visual representation
    this.mesh = this.createVisual();

    // Create physics body
    this.body = this.createPhysicsBody();

    // Add to scene and world
    this.scene.add(this.mesh);
    this.world.addBody(this.body);

    // Announce boss appearance
    this.announceBoss();
  }

  /**
   * Determine boss type based on dungeon level
   */
  private determineBossType(): DungeonBossType {
    // Boss type is determined by dungeon level
    if (this.dungeonLevel <= 3) {
      return DungeonBossType.SHADOW_KING;
    } else if (this.dungeonLevel <= 6) {
      return DungeonBossType.SPECTRAL_GUARDIAN;
    } else if (this.dungeonLevel <= 9) {
      return DungeonBossType.VOID_HARBINGER;
    } else if (this.dungeonLevel <= 12) {
      return DungeonBossType.ECTOPLASMIC_TITAN;
    } else {
      return DungeonBossType.NIGHTMARE_WEAVER;
    }
  }

  /**
   * Set base stats based on type and difficulty
   */
  private setBaseStats(): void {
    // Base stats by type - bosses are much stronger than regular enemies
    switch (this.type) {
      case DungeonBossType.SHADOW_KING:
        this.maxHealth = 200;
        this.attackPower = 15;
        this.speed = 3;
        this.pointValue = 500;
        break;
      case DungeonBossType.SPECTRAL_GUARDIAN:
        this.maxHealth = 350;
        this.attackPower = 20;
        this.speed = 3.5;
        this.pointValue = 800;
        break;
      case DungeonBossType.VOID_HARBINGER:
        this.maxHealth = 500;
        this.attackPower = 25;
        this.speed = 4;
        this.pointValue = 1200;
        break;
      case DungeonBossType.ECTOPLASMIC_TITAN:
        this.maxHealth = 700;
        this.attackPower = 30;
        this.speed = 3.5; // Slower but more powerful
        this.pointValue = 1500;
        break;
      case DungeonBossType.NIGHTMARE_WEAVER:
        this.maxHealth = 1000;
        this.attackPower = 35;
        this.speed = 5;
        this.pointValue = 2000;
        break;
      default:
        this.maxHealth = 200;
        this.attackPower = 15;
        this.speed = 3;
        this.pointValue = 500;
    }

    // Apply difficulty multiplier
    const difficultyMultiplier = {
      [DungeonDifficulty.EASY]: 0.8,
      [DungeonDifficulty.MEDIUM]: 1.0,
      [DungeonDifficulty.HARD]: 1.3,
      [DungeonDifficulty.NIGHTMARE]: 1.8
    };

    const multiplier = difficultyMultiplier[this.difficulty];

    this.maxHealth = Math.ceil(this.maxHealth * multiplier);
    this.attackPower = Math.ceil(this.attackPower * multiplier);
    this.speed = this.speed * multiplier;
    this.pointValue = Math.ceil(this.pointValue * multiplier);

    // Apply dungeon level scaling (15% increase per level)
    const levelMultiplier = 1 + (this.dungeonLevel - 1) * 0.15;

    this.maxHealth = Math.ceil(this.maxHealth * levelMultiplier);
    this.attackPower = Math.ceil(this.attackPower * levelMultiplier);
    this.speed = this.speed * Math.sqrt(levelMultiplier); // Square root for more balanced speed scaling
    this.pointValue = Math.ceil(this.pointValue * levelMultiplier);

    // Set current health to max
    this.health = this.maxHealth;
  }

  /**
   * Create visual representation based on boss type
   */
  private createVisual(): THREE.Group {
    const group = new THREE.Group();

    // Base geometry and material based on boss type
    let geometry: THREE.BufferGeometry;
    let material: THREE.Material;
    let color: number;
    let scale = 2.0; // Bosses are larger than regular enemies

    switch (this.type) {
      case DungeonBossType.SHADOW_KING:
        geometry = new THREE.SphereGeometry(1, 32, 32);
        color = 0x000066; // Dark blue
        break;
      case DungeonBossType.SPECTRAL_GUARDIAN:
        geometry = new THREE.BoxGeometry(1.5, 2.5, 1.5);
        color = 0x660066; // Purple
        break;
      case DungeonBossType.VOID_HARBINGER:
        geometry = new THREE.TorusGeometry(1.2, 0.5, 32, 32);
        color = 0x660000; // Dark red
        break;
      case DungeonBossType.ECTOPLASMIC_TITAN:
        geometry = new THREE.CylinderGeometry(1, 1.5, 3, 16);
        color = 0x006600; // Dark green
        scale = 2.5; // Even larger
        break;
      case DungeonBossType.NIGHTMARE_WEAVER:
        geometry = new THREE.IcosahedronGeometry(1.5, 1);
        color = 0x333333; // Dark gray
        scale = 3.0; // Largest boss
        break;
      default:
        geometry = new THREE.SphereGeometry(1, 32, 32);
        color = 0x000066; // Dark blue
    }

    material = new THREE.MeshStandardMaterial({
      color: color,
      emissive: color,
      emissiveIntensity: 0.7,
      transparent: true,
      opacity: 0.9
    });

    const mainBody = new THREE.Mesh(geometry, material);
    mainBody.scale.set(scale, scale, scale);
    group.add(mainBody);

    // Add crown or special feature for bosses
    if (this.type === DungeonBossType.SHADOW_KING) {
      // Add crown
      const crownGeometry = new THREE.CylinderGeometry(0.7, 1, 0.5, 5);
      const crownMaterial = new THREE.MeshStandardMaterial({
        color: 0xffcc00,
        emissive: 0xffcc00,
        emissiveIntensity: 0.5,
        metalness: 0.8,
        roughness: 0.2
      });

      const crown = new THREE.Mesh(crownGeometry, crownMaterial);
      crown.position.set(0, 1.5 * scale, 0);
      group.add(crown);
    } else if (this.type === DungeonBossType.SPECTRAL_GUARDIAN) {
      // Add armor plates
      const armorGeometry = new THREE.BoxGeometry(2, 0.3, 2);
      const armorMaterial = new THREE.MeshStandardMaterial({
        color: 0xaaaaaa,
        metalness: 0.9,
        roughness: 0.1
      });

      // Shoulder plates
      const leftShoulder = new THREE.Mesh(armorGeometry, armorMaterial);
      leftShoulder.position.set(-1.2 * scale, 0.5 * scale, 0);
      leftShoulder.scale.set(0.5, 1, 0.5);
      group.add(leftShoulder);

      const rightShoulder = new THREE.Mesh(armorGeometry, armorMaterial);
      rightShoulder.position.set(1.2 * scale, 0.5 * scale, 0);
      rightShoulder.scale.set(0.5, 1, 0.5);
      group.add(rightShoulder);
    }

    // Add eyes for all bosses
    const eyeGeometry = new THREE.SphereGeometry(0.2, 16, 16);
    const eyeMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // Left eye
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(0.4 * scale, 0.3 * scale, 0.8 * scale);
    group.add(leftEye);

    // Right eye
    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(-0.4 * scale, 0.3 * scale, 0.8 * scale);
    group.add(rightEye);

    // Add particle effects
    const particleCount = 50; // More particles for bosses
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const radius = (1.2 + Math.random() * 0.8) * scale;

      particlePositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta) - scale / 2; // Offset to center
      particlePositions[i * 3 + 2] = radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.15,
      transparent: true,
      opacity: 0.7
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    group.add(particles);

    // Add aura effect
    const auraGeometry = new THREE.SphereGeometry(scale * 1.5, 16, 16);
    const auraMaterial = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const aura = new THREE.Mesh(auraGeometry, auraMaterial);
    group.add(aura);

    // Position the group
    group.position.copy(this.position);

    return group;
  }

  /**
   * Create physics body for the boss
   */
  private createPhysicsBody(): CANNON.Body {
    // Create a sphere shape for physics
    const radius = 1.5; // Larger collision radius for boss
    const shape = new CANNON.Sphere(radius);

    // Create body
    const body = new CANNON.Body({
      mass: 20, // Heavier than regular enemies
      position: new CANNON.Vec3(this.position.x, this.position.y, this.position.z),
      shape: shape,
      material: new CANNON.Material({
        friction: 0.3,
        restitution: 0.4
      }),
      linearDamping: 0.9, // Add damping to prevent excessive sliding
      angularDamping: 0.9
    });

    return body;
  }

  /**
   * Announce boss appearance with visual and audio effects
   */
  private announceBoss(): void {
    // Play boss appearance sound
    AudioManager.playSoundEffect('bossAppear');

    // Create shockwave effect
    this.createShockwaveEffect();

    // Display boss name
    this.displayBossName();
  }

  /**
   * Create shockwave effect when boss appears
   */
  private createShockwaveEffect(): void {
    // Create ring geometry for shockwave
    const shockwaveGeometry = new THREE.RingGeometry(0.1, 0.5, 32);
    const shockwaveMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.7,
      side: THREE.DoubleSide
    });

    const shockwave = new THREE.Mesh(shockwaveGeometry, shockwaveMaterial);
    shockwave.position.copy(this.position);
    shockwave.position.y = 0.1; // Just above ground
    shockwave.rotation.x = -Math.PI / 2; // Flat on ground
    this.scene.add(shockwave);

    // Animate shockwave
    let scale = 1;
    const maxScale = 20;
    const animateShockwave = () => {
      if (scale < maxScale) {
        scale += 1;
        shockwave.scale.set(scale, scale, 1);
        shockwaveMaterial.opacity = 0.7 * (1 - scale / maxScale);
        requestAnimationFrame(animateShockwave);
      } else {
        // Remove shockwave when animation is complete
        this.scene.remove(shockwave);
        shockwaveGeometry.dispose();
        shockwaveMaterial.dispose();
      }
    };

    animateShockwave();
  }

  /**
   * Display boss name as floating text
   */
  private displayBossName(): void {
    // Create canvas for text
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;

    const context = canvas.getContext('2d');
    if (context) {
      context.fillStyle = '#000000';
      context.fillRect(0, 0, canvas.width, canvas.height);
      context.fillStyle = '#ff0000';
      context.font = 'bold 48px Arial';
      context.textAlign = 'center';
      context.textBaseline = 'middle';

      // Get boss name from type
      let bossName = '';
      switch (this.type) {
        case DungeonBossType.SHADOW_KING:
          bossName = 'SHADOW KING';
          break;
        case DungeonBossType.SPECTRAL_GUARDIAN:
          bossName = 'SPECTRAL GUARDIAN';
          break;
        case DungeonBossType.VOID_HARBINGER:
          bossName = 'VOID HARBINGER';
          break;
        case DungeonBossType.ECTOPLASMIC_TITAN:
          bossName = 'ECTOPLASMIC TITAN';
          break;
        case DungeonBossType.NIGHTMARE_WEAVER:
          bossName = 'NIGHTMARE WEAVER';
          break;
      }

      context.fillText(bossName, canvas.width / 2, canvas.height / 2);

      // Add difficulty indicator
      context.font = '24px Arial';
      context.fillStyle = '#ffffff';
      context.fillText(`Level ${this.dungeonLevel} - ${this.difficulty}`, canvas.width / 2, canvas.height / 2 + 40);
    }

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0,
      side: THREE.DoubleSide
    });

    const geometry = new THREE.PlaneGeometry(5, 1.25);
    const textMesh = new THREE.Mesh(geometry, material);

    // Position above boss
    textMesh.position.copy(this.position);
    textMesh.position.y += 5;
    textMesh.rotation.x = -Math.PI / 6; // Tilt slightly

    this.scene.add(textMesh);

    // Animate text appearance and disappearance
    let time = 0;
    const fadeInDuration = 1.0;
    const holdDuration = 3.0;
    const fadeOutDuration = 1.0;
    const totalDuration = fadeInDuration + holdDuration + fadeOutDuration;

    const animateText = () => {
      time += 0.016; // Approximately 60fps

      if (time < fadeInDuration) {
        // Fade in
        material.opacity = time / fadeInDuration;
      } else if (time < fadeInDuration + holdDuration) {
        // Hold
        material.opacity = 1.0;
      } else if (time < totalDuration) {
        // Fade out
        material.opacity = 1.0 - (time - fadeInDuration - holdDuration) / fadeOutDuration;
      } else {
        // Remove text
        this.scene.remove(textMesh);
        geometry.dispose();
        material.dispose();
        return;
      }

      requestAnimationFrame(animateText);
    };

    animateText();
  }

  /**
   * Update boss state
   */
  public update(delta: number, playerPosition: THREE.Vector3): void {
    if (this.isDefeated()) return;

    // Update attack cooldowns
    if (this.attackCooldown > 0) {
      this.attackCooldown -= delta;
    }

    if (this.specialAttackCooldown > 0) {
      this.specialAttackCooldown -= delta;
    }

    // Update position from physics
    this.mesh.position.copy(this.body.position);

    // Calculate distance to player
    const distanceToPlayer = this.mesh.position.distanceTo(playerPosition);

    // If player is within detection range, engage
    if (distanceToPlayer < this.detectionRange) {
      // Calculate direction to player
      const direction = new THREE.Vector3()
        .subVectors(playerPosition, this.mesh.position)
        .normalize();

      // Look at player
      this.mesh.lookAt(playerPosition);

      // Check if we should change attack phase based on health
      this.checkPhaseChange();

      // If within attack range and cooldown is complete, attack
      if (distanceToPlayer < this.attackRange && this.attackCooldown <= 0) {
        // Decide between normal and special attack
        if (this.specialAttackCooldown <= 0 && Math.random() < 0.3) {
          this.specialAttack();
        } else {
          this.attack();
        }
      } else {
        // Otherwise move towards player
        const force = direction.multiplyScalar(this.speed * 15 * delta);

        this.body.applyForce(
          new CANNON.Vec3(force.x, force.y, force.z),
          new CANNON.Vec3(0, 0, 0)
        );
      }
    } else {
      // Random movement when player is not detected
      if (Math.random() < 0.01) {
        const randomDirection = new THREE.Vector3(
          Math.random() * 2 - 1,
          0,
          Math.random() * 2 - 1
        ).normalize();

        const force = randomDirection.multiplyScalar(this.speed * 5 * delta);

        this.body.applyForce(
          new CANNON.Vec3(force.x, force.y, force.z),
          new CANNON.Vec3(0, 0, 0)
        );
      }
    }

    // Animate particles and effects
    this.animateEffects(delta);
  }

  /**
   * Check if we should change attack phase based on health
   */
  private checkPhaseChange(): void {
    const healthPercent = this.health / this.maxHealth;

    // Check if we've crossed a threshold
    if (this.attackPhase < this.healthThresholds.length &&
        healthPercent <= this.healthThresholds[this.attackPhase]) {
      // Advance to next phase
      this.attackPhase++;

      // Play phase change effect
      this.phaseChangeEffect();

      // Increase attack power for later phases
      this.attackPower = Math.ceil(this.attackPower * 1.2);
    }
  }

  /**
   * Create visual effect for phase change
   */
  private phaseChangeEffect(): void {
    // Play sound
    AudioManager.playSoundEffect('bossPhaseChange');

    // Create pulse effect
    const pulseGeometry = new THREE.SphereGeometry(3, 32, 32);
    const pulseMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      transparent: true,
      opacity: 0.7,
      side: THREE.DoubleSide
    });

    const pulse = new THREE.Mesh(pulseGeometry, pulseMaterial);
    pulse.position.copy(this.position);
    this.scene.add(pulse);

    // Animate pulse
    let scale = 1;
    const maxScale = 5;
    const animatePulse = () => {
      if (scale < maxScale) {
        scale += 0.2;
        pulse.scale.set(scale, scale, scale);
        pulseMaterial.opacity = 0.7 * (1 - scale / maxScale);
        requestAnimationFrame(animatePulse);
      } else {
        // Remove pulse when animation is complete
        this.scene.remove(pulse);
        pulseGeometry.dispose();
        pulseMaterial.dispose();
      }
    };

    animatePulse();
  }

  /**
   * Animate boss effects
   */
  private animateEffects(delta: number): void {
    // Find particles in the mesh group
    this.mesh.traverse(child => {
      if (child instanceof THREE.Points) {
        // Get positions
        const positions = child.geometry.attributes.position.array;

        // Animate each particle
        for (let i = 0; i < positions.length; i += 3) {
          // Oscillate particles
          positions[i] += Math.sin(Date.now() * 0.001 + i) * 0.02;
          positions[i + 1] += Math.cos(Date.now() * 0.002 + i) * 0.02;
          positions[i + 2] += Math.sin(Date.now() * 0.001 + i) * 0.02;
        }

        // Update geometry
        child.geometry.attributes.position.needsUpdate = true;
      }

      // Animate aura
      if (child instanceof THREE.Mesh &&
          child.material instanceof THREE.MeshBasicMaterial &&
          child.material.opacity < 0.2) { // Identify aura by low opacity

        // Pulse aura
        child.scale.x = 1.0 + Math.sin(Date.now() * 0.001) * 0.1;
        child.scale.y = 1.0 + Math.sin(Date.now() * 0.001) * 0.1;
        child.scale.z = 1.0 + Math.sin(Date.now() * 0.001) * 0.1;
      }
    });
  }

  /**
   * Perform normal attack
   */
  private attack(): void {
    // Set cooldown
    this.attackCooldown = 1.5;

    // Set attacking flag
    this.isAttacking = true;

    // Play attack sound
    AudioManager.playSoundEffect('bossAttack');

    // Visual attack effect
    this.createAttackEffect();

    // Reset attacking flag after a short delay
    setTimeout(() => {
      this.isAttacking = false;
    }, 500);
  }

  /**
   * Perform special attack
   */
  private specialAttack(): void {
    // Set cooldowns
    this.attackCooldown = 1.0;
    this.specialAttackCooldown = 8.0; // Longer cooldown for special attack

    // Set special attacking flag
    this.isSpecialAttacking = true;

    // Play special attack sound
    AudioManager.playSoundEffect('bossSpecialAttack');

    // Visual special attack effect based on boss type
    switch (this.type) {
      case DungeonBossType.SHADOW_KING:
        this.createShadowBlastEffect();
        break;
      case DungeonBossType.SPECTRAL_GUARDIAN:
        this.createSpectralWaveEffect();
        break;
      case DungeonBossType.VOID_HARBINGER:
        this.createVoidRiftEffect();
        break;
      case DungeonBossType.ECTOPLASMIC_TITAN:
        this.createEctoplasmicBurstEffect();
        break;
      case DungeonBossType.NIGHTMARE_WEAVER:
        this.createNightmareVortexEffect();
        break;
    }

    // Reset special attacking flag after a short delay
    setTimeout(() => {
      this.isSpecialAttacking = false;
    }, 1000);
  }

  /**
   * Create visual effect for normal attack
   */
  private createAttackEffect(): void {
    // Create attack particles
    const particleCount = 20;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Get boss position
    const position = this.mesh.position.clone();

    // Create particles in a cone shape in front of boss
    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.random() - 0.5) * Math.PI / 2;
      const distance = 1 + Math.random() * 3;

      // Calculate direction based on boss rotation
      const direction = new THREE.Vector3(0, 0, 1);
      direction.applyQuaternion(this.mesh.quaternion);

      // Add randomness to direction
      direction.x += Math.sin(angle) * 0.5;
      direction.y += (Math.random() - 0.5) * 0.2;

      // Set particle position
      particlePositions[i * 3] = position.x + direction.x * distance;
      particlePositions[i * 3 + 1] = position.y + direction.y * distance;
      particlePositions[i * 3 + 2] = position.z + direction.z * distance;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Get color based on boss type
    let color: number;
    switch (this.type) {
      case DungeonBossType.SHADOW_KING: color = 0x000066; break;
      case DungeonBossType.SPECTRAL_GUARDIAN: color = 0x660066; break;
      case DungeonBossType.VOID_HARBINGER: color = 0x660000; break;
      case DungeonBossType.ECTOPLASMIC_TITAN: color = 0x006600; break;
      case DungeonBossType.NIGHTMARE_WEAVER: color = 0x333333; break;
      default: color = 0x000066;
    }

    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.3,
      transparent: true,
      opacity: 0.8
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);

    // Animate and remove particles after a short time
    let time = 0;
    const duration = 0.5;

    const animateParticles = () => {
      time += 0.016; // Approximately 60fps

      if (time < duration) {
        // Move particles outward
        const positions = particleGeometry.attributes.position.array;

        for (let i = 0; i < particleCount; i++) {
          const index = i * 3;
          const dirX = positions[index] - position.x;
          const dirY = positions[index + 1] - position.y;
          const dirZ = positions[index + 2] - position.z;

          positions[index] += dirX * 0.1;
          positions[index + 1] += dirY * 0.1;
          positions[index + 2] += dirZ * 0.1;
        }

        particleGeometry.attributes.position.needsUpdate = true;

        // Fade out
        particleMaterial.opacity = 0.8 * (1 - time / duration);

        requestAnimationFrame(animateParticles);
      } else {
        // Remove particles
        this.scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animateParticles();
  }

  // Special attack effect methods (simplified implementations)
  private createShadowBlastEffect(): void {
    // Create shadow blast effect (simplified)
    const geometry = new THREE.SphereGeometry(0.5, 32, 32);
    const material = new THREE.MeshBasicMaterial({
      color: 0x000066,
      transparent: true,
      opacity: 0.8
    });

    const blast = new THREE.Mesh(geometry, material);
    blast.position.copy(this.position);
    this.scene.add(blast);

    // Animate blast
    let scale = 1;
    const maxScale = 10;
    const animateBlast = () => {
      if (scale < maxScale) {
        scale += 0.5;
        blast.scale.set(scale, scale, scale);
        material.opacity = 0.8 * (1 - scale / maxScale);
        requestAnimationFrame(animateBlast);
      } else {
        // Remove blast when animation is complete
        this.scene.remove(blast);
        geometry.dispose();
        material.dispose();
      }
    };

    animateBlast();
  }

  private createSpectralWaveEffect(): void {
    // Simplified implementation
    this.createShadowBlastEffect(); // Reuse for now
  }

  private createVoidRiftEffect(): void {
    // Simplified implementation
    this.createShadowBlastEffect(); // Reuse for now
  }

  private createEctoplasmicBurstEffect(): void {
    // Simplified implementation
    this.createShadowBlastEffect(); // Reuse for now
  }

  private createNightmareVortexEffect(): void {
    // Simplified implementation
    this.createShadowBlastEffect(); // Reuse for now
  }

  /**
   * Take damage from player or pet
   */
  public takeDamage(amount: number): void {
    if (this.isDefeated()) return;

    // Reduce health
    this.health -= amount;

    // Check if defeated
    if (this.health <= 0) {
      this.defeat();
    } else {
      // Visual feedback for taking damage
      this.showDamageEffect();
    }
  }

  /**
   * Show visual effect when taking damage
   */
  private showDamageEffect(): void {
    // Flash boss red
    this.mesh.traverse(child => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshStandardMaterial) {
        // Store original color
        const originalColor = child.material.color.clone();
        const originalEmissive = child.material.emissive.clone();

        // Flash red
        child.material.color.set(0xff0000);
        child.material.emissive.set(0xff0000);

        // Restore original color after flash
        setTimeout(() => {
          if (child.material instanceof THREE.MeshStandardMaterial) {
            child.material.color.copy(originalColor);
            child.material.emissive.copy(originalEmissive);
          }
        }, 100);
      }
    });

    // Play damage sound
    AudioManager.playSoundEffect('bossHit');
  }

  /**
   * Defeat the boss
   */
  private defeat(): void {
    this._isDefeated = true;

    // Play defeat sound
    AudioManager.playSoundEffect('bossDefeat');

    // Create defeat effect
    this.createDefeatEffect();
  }

  /**
   * Create visual effect for boss defeat
   */
  private createDefeatEffect(): void {
    // Create explosion particles
    const particleCount = 100;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Get boss position
    const position = this.mesh.position.clone();

    // Create particles in a sphere around boss
    for (let i = 0; i < particleCount; i++) {
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const radius = Math.random() * 5;

      particlePositions[i * 3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = position.z + radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Get color based on boss type
    let color: number;
    switch (this.type) {
      case DungeonBossType.SHADOW_KING: color = 0x000066; break;
      case DungeonBossType.SPECTRAL_GUARDIAN: color = 0x660066; break;
      case DungeonBossType.VOID_HARBINGER: color = 0x660000; break;
      case DungeonBossType.ECTOPLASMIC_TITAN: color = 0x006600; break;
      case DungeonBossType.NIGHTMARE_WEAVER: color = 0x333333; break;
      default: color = 0x000066;
    }

    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.5,
      transparent: true,
      opacity: 1.0
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);

    // Animate and remove particles
    let time = 0;
    const duration = 2.0;

    const animateParticles = () => {
      time += 0.016; // Approximately 60fps

      if (time < duration) {
        // Move particles outward
        const positions = particleGeometry.attributes.position.array;

        for (let i = 0; i < particleCount; i++) {
          const index = i * 3;
          const dirX = positions[index] - position.x;
          const dirY = positions[index + 1] - position.y;
          const dirZ = positions[index + 2] - position.z;

          positions[index] += dirX * 0.05;
          positions[index + 1] += dirY * 0.05;
          positions[index + 2] += dirZ * 0.05;
        }

        particleGeometry.attributes.position.needsUpdate = true;

        // Fade out
        particleMaterial.opacity = 1.0 * (1 - time / duration);

        requestAnimationFrame(animateParticles);
      } else {
        // Remove particles
        this.scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animateParticles();

    // Hide boss mesh
    this.mesh.visible = false;

    // Create final explosion
    const explosionGeometry = new THREE.SphereGeometry(1, 32, 32);
    const explosionMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 1.0
    });

    const explosion = new THREE.Mesh(explosionGeometry, explosionMaterial);
    explosion.position.copy(position);
    this.scene.add(explosion);

    // Animate explosion
    let expScale = 1;
    const expMaxScale = 15;
    const animateExplosion = () => {
      if (expScale < expMaxScale) {
        expScale += 1;
        explosion.scale.set(expScale, expScale, expScale);
        explosionMaterial.opacity = 1.0 * (1 - expScale / expMaxScale);
        requestAnimationFrame(animateExplosion);
      } else {
        // Remove explosion when animation is complete
        this.scene.remove(explosion);
        explosionGeometry.dispose();
        explosionMaterial.dispose();
      }
    };

    animateExplosion();
  }

  /**
   * Get boss position
   */
  public getPosition(): THREE.Vector3 {
    return this.mesh.position.clone();
  }

  /**
   * Check if boss is defeated
   */
  public isDefeated(): boolean {
    return this._isDefeated;
  }

  /**
   * Get boss data for scoring/rewards
   */
  public getBossData(): any {
    return {
      type: this.type,
      points: this.pointValue,
      position: this.getPosition(),
      difficulty: this.difficulty,
      dungeonLevel: this.dungeonLevel
    };
  }

  /**
   * Get attack power
   */
  public getAttackPower(): number {
    return this.attackPower;
  }

  /**
   * Dispose of all resources
   */
  public dispose(): void {
    // Remove from scene
    this.scene.remove(this.mesh);

    // Remove from physics world
    this.world.removeBody(this.body);

    // Dispose of geometries and materials
    this.mesh.traverse(child => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) child.geometry.dispose();

        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else if (child.material) {
          child.material.dispose();
        }
      }
    });
  }
}