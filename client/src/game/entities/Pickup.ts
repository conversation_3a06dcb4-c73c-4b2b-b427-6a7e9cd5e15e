import * as THREE from 'three';
import * as CA<PERSON><PERSON><PERSON> from 'cannon-es';
import { AmmoType } from '../types.js';

export enum PickupType {
  Health = 'health',
  Ammo = 'ammo',
  HomingMissile = 'homing_missile'
}

interface PickupOptions {
  amount: number;
  ammoType?: AmmoType;
  lifespan?: number; // How long the pickup stays in the world (seconds)
}

class Pickup {
  private mesh: THREE.Group;
  private body: CANNON.Body;
  private scene: THREE.Scene;
  private world: CANNON.World;
  private type: PickupType;
  private options: PickupOptions;
  private timeLeft: number = 15; // Default lifespan
  private rotationSpeed: number;
  private bounceAnimation: {
    height: number;
    speed: number;
    time: number;
  };
  
  constructor(
    scene: THREE.Scene, 
    world: CANNON.World, 
    position: THREE.Vector3,
    type: PickupType,
    options: PickupOptions
  ) {
    this.scene = scene;
    this.world = world;
    this.type = type;
    this.options = {
      ...options,
      lifespan: options.lifespan || 15 // Default 15 seconds if not specified
    };
    this.timeLeft = this.options.lifespan || 15;
    
    // Random rotation speed for visual interest
    this.rotationSpeed = Math.random() * 2 + 1;
    
    // Bounce animation settings
    this.bounceAnimation = {
      height: 0.2, // Maximum bounce height
      speed: 1.5, // Bounce cycles per second
      time: Math.random() * Math.PI * 2 // Random starting phase
    };
    
    // Create visual representation
    this.mesh = new THREE.Group();
    this.createVisual();
    
    // Position the pickup
    this.mesh.position.copy(position);
    this.scene.add(this.mesh);
    
    // Create physics body
    const shape = new CANNON.Sphere(0.5);
    this.body = new CANNON.Body({
      mass: 1,
      shape: shape,
      position: new CANNON.Vec3(position.x, position.y, position.z),
      collisionFilterGroup: 4, // Pickup group
      collisionFilterMask: 1 | 2, // Collide with world and player
      material: new CANNON.Material({
        friction: 0.3,
        restitution: 0.4
      })
    });
    
    // Add body to world
    this.world.addBody(this.body);
    
    // Add initial impulse to scatter pickups
    const randomDirection = new CANNON.Vec3(
      (Math.random() - 0.5) * 10,
      Math.random() * 5 + 3,
      (Math.random() - 0.5) * 10
    );
    this.body.applyImpulse(randomDirection, new CANNON.Vec3(0, 0, 0));
  }
  
  update(delta: number): boolean {
    // Update remaining time
    this.timeLeft -= delta;
    
    // If time's up, remove the pickup
    if (this.timeLeft <= 0) {
      this.dispose();
      return false;
    }
    
    // Update position from physics
    this.mesh.position.set(
      this.body.position.x,
      this.body.position.y,
      this.body.position.z
    );
    
    // Visual effects - rotation and bounce
    this.mesh.rotation.y += this.rotationSpeed * delta;
    
    // Update the bounce animation time
    this.bounceAnimation.time += delta * this.bounceAnimation.speed * Math.PI * 2;
    
    // Apply hover/bounce effect
    const hoverOffset = Math.sin(this.bounceAnimation.time) * this.bounceAnimation.height;
    this.mesh.position.y += hoverOffset;
    
    // Flash/fade if about to expire (last 3 seconds)
    if (this.timeLeft < 3) {
      const opacity = (Math.sin(this.timeLeft * 10) * 0.5 + 0.5) * (this.timeLeft / 3);
      this.mesh.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshStandardMaterial) {
          child.material.opacity = Math.max(0.2, opacity);
        }
      });
    }
    
    return true;
  }
  
  getPosition(): THREE.Vector3 {
    return new THREE.Vector3(
      this.body.position.x,
      this.body.position.y,
      this.body.position.z
    );
  }
  
  getType(): PickupType {
    return this.type;
  }
  
  getOptions(): PickupOptions {
    return this.options;
  }
  
  dispose(): void {
    // Remove from physics world
    this.world.removeBody(this.body);
    
    // Remove from scene
    this.scene.remove(this.mesh);
    
    // Dispose of geometries and materials
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) child.geometry.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else if (child.material) {
          child.material.dispose();
        }
      }
    });
  }
  
  private createVisual(): void {
    if (this.type === PickupType.Health) {
      // Create health pickup visual (red cross)
      this.createHealthPickupVisual();
    } else if (this.type === PickupType.HomingMissile) {
      // Create homing missile pickup visual
      this.createHomingMissileVisual();
    } else {
      // Create ammo pickup visual (based on ammo type)
      this.createAmmoPickupVisual();
    }
  }
  
  private createHomingMissileVisual(): void {
    // Base
    const baseGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.2, 8);
    const baseMaterial = new THREE.MeshStandardMaterial({
      color: 0x333333,
      metalness: 0.8,
      roughness: 0.2
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    this.mesh.add(base);
    
    // Missile body
    const missileGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.8, 8);
    const missileMaterial = new THREE.MeshStandardMaterial({
      color: 0xdd2222,
      emissive: 0x550000,
      emissiveIntensity: 0.3,
      metalness: 0.7,
      roughness: 0.3
    });
    const missile = new THREE.Mesh(missileGeometry, missileMaterial);
    missile.position.y = 0.4;
    this.mesh.add(missile);
    
    // Missile tip
    const tipGeometry = new THREE.ConeGeometry(0.15, 0.3, 8);
    const tipMaterial = new THREE.MeshStandardMaterial({
      color: 0xdddddd,
      metalness: 0.9,
      roughness: 0.1
    });
    const tip = new THREE.Mesh(tipGeometry, tipMaterial);
    tip.position.y = 0.95;
    this.mesh.add(tip);
    
    // Fins (4)
    const finGeometry = new THREE.BoxGeometry(0.05, 0.2, 0.2);
    const finMaterial = new THREE.MeshStandardMaterial({
      color: 0xaaaaaa,
      metalness: 0.6,
      roughness: 0.4
    });
    
    // Position fins around the missile
    for (let i = 0; i < 4; i++) {
      const fin = new THREE.Mesh(finGeometry, finMaterial);
      const angle = (i * Math.PI / 2); // 4 fins at 90-degree intervals
      const radius = 0.2;
      
      fin.position.set(
        Math.cos(angle) * radius,
        0.25, // Bottom part of missile
        Math.sin(angle) * radius
      );
      
      // Rotate fin to point outward
      fin.rotation.y = angle + Math.PI / 2;
      
      this.mesh.add(fin);
    }
    
    // Add homing indicator ring
    const ringGeometry = new THREE.TorusGeometry(0.3, 0.03, 8, 16);
    const ringMaterial = new THREE.MeshStandardMaterial({
      color: 0x00ffff,
      emissive: 0x00ffff,
      emissiveIntensity: 0.7,
      transparent: true,
      opacity: 0.8
    });
    const ring = new THREE.Mesh(ringGeometry, ringMaterial);
    ring.position.y = 0.6;
    ring.rotation.x = Math.PI / 2; // Make ring horizontal
    this.mesh.add(ring);
    
    // Add a point light for glow effect
    const light = new THREE.PointLight(0x00ffff, 1, 3);
    light.position.set(0, 0.6, 0);
    this.mesh.add(light);
  }
  
  private createHealthPickupVisual(): void {
    // Base
    const baseGeometry = new THREE.BoxGeometry(0.6, 0.15, 0.6);
    const baseMaterial = new THREE.MeshStandardMaterial({
      color: 0xaa0000,
      emissive: 0x330000,
      metalness: 0.7,
      roughness: 0.3,
      transparent: true,
      opacity: 0.9
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    this.mesh.add(base);
    
    // Cross vertical
    const verticalGeometry = new THREE.BoxGeometry(0.2, 0.7, 0.2);
    const crossMaterial = new THREE.MeshStandardMaterial({
      color: 0xff0000,
      emissive: 0xff0000,
      emissiveIntensity: 0.5,
      metalness: 0.7,
      roughness: 0.3,
      transparent: true,
      opacity: 0.9
    });
    const verticalPart = new THREE.Mesh(verticalGeometry, crossMaterial);
    verticalPart.position.y = 0.35;
    this.mesh.add(verticalPart);
    
    // Cross horizontal
    const horizontalGeometry = new THREE.BoxGeometry(0.7, 0.2, 0.2);
    const horizontalPart = new THREE.Mesh(horizontalGeometry, crossMaterial);
    horizontalPart.position.y = 0.35;
    this.mesh.add(horizontalPart);
    
    // Add a point light for glow effect
    const light = new THREE.PointLight(0xff0000, 0.8, 2);
    light.position.set(0, 0.3, 0);
    this.mesh.add(light);
  }
  
  private createAmmoPickupVisual(): void {
    // Determine color based on ammo type
    let color: number;
    let emissiveColor: number;
    
    switch (this.options.ammoType) {
      case AmmoType.GravityWell:
        color = 0x00ffff;
        emissiveColor = 0x00ffff;
        break;
      case AmmoType.TimeBubble:
        color = 0xff00ff;
        emissiveColor = 0xff00ff;
        break;
      case AmmoType.PhaseNet:
        color = 0xffff00;
        emissiveColor = 0xffff00;
        break;
      default:
        color = 0x0088ff;
        emissiveColor = 0x0000ff;
    }
    
    // Base
    const baseGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.2, 8);
    const baseMaterial = new THREE.MeshStandardMaterial({
      color: 0x333333,
      metalness: 0.7,
      roughness: 0.3
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    this.mesh.add(base);
    
    // Ammo canister
    const canisterGeometry = new THREE.CylinderGeometry(0.25, 0.25, 0.6, 8);
    const canisterMaterial = new THREE.MeshStandardMaterial({
      color: color,
      emissive: emissiveColor,
      emissiveIntensity: 0.3,
      metalness: 0.6,
      roughness: 0.4,
      transparent: true,
      opacity: 0.8
    });
    const canister = new THREE.Mesh(canisterGeometry, canisterMaterial);
    canister.position.y = 0.3;
    this.mesh.add(canister);
    
    // Top cap
    const capGeometry = new THREE.CylinderGeometry(0.15, 0.25, 0.1, 8);
    const capMaterial = new THREE.MeshStandardMaterial({
      color: 0x444444,
      metalness: 0.7,
      roughness: 0.3
    });
    const cap = new THREE.Mesh(capGeometry, capMaterial);
    cap.position.y = 0.65;
    this.mesh.add(cap);
    
    // Add a point light for glow effect
    const light = new THREE.PointLight(color, 0.8, 2);
    light.position.set(0, 0.3, 0);
    this.mesh.add(light);
  }
}

export default Pickup;