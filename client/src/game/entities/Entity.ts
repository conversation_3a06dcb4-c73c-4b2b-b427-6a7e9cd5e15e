import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';

/**
 * Base class for all game entities
 */
export default class Entity {
  protected mesh: THREE.Group | THREE.Mesh | null = null;
  protected body: CANNON.Body | null = null;
  protected scene: THREE.Scene;
  protected world: CANNON.World;

  constructor(scene: THREE.Scene, world: CANNON.World) {
    this.scene = scene;
    this.world = world;
  }

  /**
   * Update entity state
   */
  update(delta: number, playerPosition?: THREE.Vector3, player?: any): void {
    // Base implementation does nothing, should be overridden
  }

  /**
   * Get entity position
   */
  getPosition(): THREE.Vector3 {
    if (this.body) {
      return new THREE.Vector3(
        this.body.position.x,
        this.body.position.y,
        this.body.position.z
      );
    }
    return new THREE.Vector3();
  }

  /**
   * Get entity physics body
   */
  getPhysicsBody(): CANNON.Body {
    if (!this.body) {
      throw new Error('Entity has no physics body');
    }
    return this.body;
  }

  /**
   * Get entity mesh
   */
  getMesh(): THREE.Group | THREE.Mesh | null {
    return this.mesh;
  }

  /**
   * Remove entity from scene and physics world
   */
  dispose(): void {
    if (this.mesh && this.scene) {
      this.scene.remove(this.mesh);
    }

    if (this.body && this.world) {
      this.world.removeBody(this.body);
    }
  }
}