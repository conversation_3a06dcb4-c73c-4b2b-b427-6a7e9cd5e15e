import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import Entity from './Entity.js';

/**
 * Base Enemy class for all enemy types
 */
export default class Enemy extends Entity {
  protected mesh: THREE.Group | null = null;
  protected body: CANNON.Body | null = null;
  protected health: number = 100;
  protected maxHealth: number = 100;
  protected attackPower: number = 10;
  protected speed: number = 2;
  protected isDefeated: boolean = false;

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3
  ) {
    super(scene, world);

    // Create visual representation
    this.mesh = this.createModel();
    this.mesh.position.copy(position);

    // Create physics body
    this.body = this.createPhysicsBody(position);

    // Add to scene and world
    this.scene.add(this.mesh);
    this.world.addBody(this.body);
  }

  /**
   * Create the visual model for the enemy
   */
  private createModel(): THREE.Group {
    const group = new THREE.Group();

    // Create sprite-based enemy
    const spriteMap = new THREE.TextureLoader().load('/assets/sprites/enemy.png');
    const spriteMaterial = new THREE.SpriteMaterial({
      map: spriteMap,
      color: 0xffffff,
      transparent: true,
      alphaTest: 0.5
    });

    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(1, 1.5, 1);
    group.add(sprite);

    return group;
  }

  /**
   * Create physics body for the enemy
   */
  private createPhysicsBody(position: THREE.Vector3): CANNON.Body {
    const shape = new CANNON.Sphere(1);
    const body = new CANNON.Body({
      mass: 1,
      shape: shape,
      position: new CANNON.Vec3(position.x, position.y, position.z),
      linearDamping: 0.1
    });

    return body;
  }

  /**
   * Update enemy state
   */
  public update(_delta: number): void {
    if (this.isDefeated || !this.mesh || !this.body) return;

    // Update mesh position from physics body
    this.mesh.position.copy(this.body.position);
  }

  /**
   * Update enemy with player position for AI behavior
   */
  public updateWithPlayer(delta: number, playerPosition: THREE.Vector3): void {
    if (this.isDefeated || !this.mesh || !this.body) return;

    // Update mesh position from physics body
    this.mesh.position.copy(this.body.position);

    // Basic AI: move towards player
    const direction = new THREE.Vector3()
      .subVectors(playerPosition, this.mesh.position)
      .normalize();

    const force = direction.multiplyScalar(this.speed * 10 * delta);
    this.body.applyForce(
      new CANNON.Vec3(force.x, force.y, force.z),
      new CANNON.Vec3(0, 0, 0)
    );
  }

  /**
   * Take damage
   */
  public takeDamage(amount: number): void {
    if (this.isDefeated) return;

    this.health -= amount;

    if (this.health <= 0) {
      this.defeat();
    }
  }

  /**
   * Defeat the enemy
   */
  protected defeat(): void {
    this.isDefeated = true;
    this.dispose();
  }

  /**
   * Get enemy mesh
   */
  public getMesh(): THREE.Group | null {
    return this.mesh;
  }

  /**
   * Check if enemy is defeated
   */
  public isEnemyDefeated(): boolean {
    return this.isDefeated;
  }

  /**
   * Get attack power
   */
  public getAttackPower(): number {
    return this.attackPower;
  }
}