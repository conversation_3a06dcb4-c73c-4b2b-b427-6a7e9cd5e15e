import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import Specter from './Specter.js';
import Player from './Player.js';
import { SpecterType } from '../types.js';
import { audioManager } from '../audio/AudioManager.js';
import { PetService } from '../../services/petService.js';

/**
 * OrangeEnemy class - special enemy that can be converted to a pet
 */
export default class OrangeEnemy extends Specter {
  private isDefeated: boolean = false;
  private static readonly ORANGE_COLOR: string = '#FF6600';

  // Add properties that were missing and causing TypeScript errors
  protected health: number = 150;
  protected specterType: SpecterType;

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    physicsMaterial: CANNON.Material,
  ) {
    // Create a custom specter type for the Orange enemy
    const orangeType: SpecterType = {
      id: 999, // Special ID for Orange enemy
      name: "Orange",
      color: OrangeEnemy.ORANGE_COLOR,
      points: 200, // Higher point value
      texture: '/assets/textures/orangenemy.png'
    };

    // Call parent constructor with our custom type
    super(scene, world, position, physicsMaterial, orangeType);

    // Store the specter type for later use
    this.specterType = orangeType;

    // Override default specter properties
    this.health = 150; // More health than regular specters

    // Use the setter method instead of directly accessing private property
    this.setSpeed(2.5); // Slightly slower than regular specters

    // Apply orange color to the mesh
    this.getMesh().traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        material.color.set(OrangeEnemy.ORANGE_COLOR);
      }
    });

    // Add custom userData to identify this as an OrangeEnemy
    this.getMesh().userData.isOrangeEnemy = true;
    this.getMesh().userData.orangeEnemyReference = this;
  }

  /**
   * Override the update method to customize behavior
   */
  update(delta: number, playerPosition: THREE.Vector3, player?: Player): void {
    if (this.isDefeated) return;

    // Call parent update method for basic behavior
    super.update(delta, playerPosition, player);

    // Add any custom behavior here
    // For now, just use the standard specter behavior
  }

  /**
   * Override takeDamage to handle defeat differently
   */
  takeDamage(amount: number): void {
    if (this.isDefeated) return;

    this.health -= amount;

    // Flash the mesh to indicate damage
    const mesh = this.getMesh();
    mesh.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        const originalColor = material.color.clone();
        material.color.set(0xffffff);

        setTimeout(() => {
          if (material) {
            material.color.copy(originalColor);
          }
        }, 100);
      }
    });

    // Check if defeated
    if (this.health <= 0) {
      this.defeat();
    }
  }

  /**
   * Custom defeat method - converts to pet specter when defeated
   */
  defeat(): void {
    if (this.isDefeated) return;

    this.isDefeated = true;

    // Check if player already has an Orange Pet Specter
    PetService.hasOrangePetSpecter().then(hasOrangePet => {
      if (hasOrangePet) {
        console.log('Player already has an Orange Pet Specter, not converting this enemy');
        // Just create a regular defeat effect
        this.createRegularDefeatEffect();
        
        // Remove from scene
        const mesh = this.getMesh();
        const scene = this.getMesh().parent as THREE.Scene;
        if (scene && mesh) {
          scene.remove(mesh);
        }
        
        // Remove physics body
        const body = this.getPhysicsBody();
        if (body) {
          if (body.world) {
            body.world.removeBody(body);
          }
        }
        
        return;
      }

      // Continue with transformation if player doesn't have an Orange Pet
      console.log('Player does not have an Orange Pet Specter, converting this enemy');
      
      // Create a special effect to indicate transformation
      this.createTransformationEffect();

      // Make semi-transparent during transformation
      const mesh = this.getMesh();
      mesh.traverse((child) => {
        if (child instanceof THREE.Sprite) {
          const material = child.material as THREE.SpriteMaterial;
          material.opacity = 0.5;
        }
      });

      // Remove physics body
      const body = this.getPhysicsBody();
      const scene = this.getMesh().parent as THREE.Scene;
      if (body) {
        // We need to get the world from the body since we don't have direct access
        if (body.world) {
          body.world.removeBody(body);
        }
      }

      // Trigger conversion to pet specter
      // This will be handled by the GameEngine
      // We'll dispatch a custom event that GameEngine will listen for
      const customEvent = new CustomEvent('orangeEnemyDefeated', {
        detail: {
          position: this.getPosition(),
          type: this.specterType,
          mesh: mesh,
          texture: this.specterType.texture // Pass the texture explicitly
        }
      });
      window.dispatchEvent(customEvent);

      // Remove the mesh from the scene after dispatching the event
      // This fixes the issue where the enemy texture stays in the scene
      if (scene && mesh) {
        // Delay removal slightly to ensure event is processed
        setTimeout(() => {
          scene.remove(mesh);
        }, 100);
      }
    }).catch(error => {
      console.error('Error checking if player has Orange Pet Specter:', error);
      // Fallback to regular defeat behavior
      this.createRegularDefeatEffect();
      
      // Remove from scene
      const mesh = this.getMesh();
      const scene = this.getMesh().parent as THREE.Scene;
      if (scene && mesh) {
        scene.remove(mesh);
      }
      
      // Remove physics body
      const body = this.getPhysicsBody();
      if (body) {
        if (body.world) {
          body.world.removeBody(body);
        }
      }
    });
  }

  /**
   * Create a regular defeat effect for when the Orange Enemy is defeated
   * but not converted to a pet (e.g., player already has one)
   */
  private createRegularDefeatEffect(): void {
    const position = this.getPosition();
    // Get the scene from the mesh's parent
    const scene = this.getMesh().parent as THREE.Scene;

    // Create particles for defeat effect
    const particleCount = 30;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Initialize particles in a sphere around the enemy
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      const radius = 1;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particlePositions[i3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i3 + 2] = position.z + radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Create orange glowing particles
    const particleMaterial = new THREE.PointsMaterial({
      color: OrangeEnemy.ORANGE_COLOR,
      size: 0.3,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Play defeat sound
    audioManager.playSoundEffect('specterDefeated');

    // Animate particles exploding outward
    let time = 0;
    const duration = 1.5; // seconds

    const animate = () => {
      time += 0.016; // Approximately 60fps

      // Move particles outward
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        const angle = (i / particleCount) * Math.PI * 2;
        const x = particlePositions[i3];
        const y = particlePositions[i3 + 1];
        const z = particlePositions[i3 + 2];
        
        // Move away from center
        const direction = new THREE.Vector3(x - position.x, y - position.y, z - position.z).normalize();
        particlePositions[i3] += direction.x * 0.1;
        particlePositions[i3 + 1] += direction.y * 0.1;
        particlePositions[i3 + 2] += direction.z * 0.1;
      }
      
      particleGeometry.attributes.position.needsUpdate = true;

      // Fade out
      particleMaterial.opacity = 0.8 * (1 - time / duration);

      if (time < duration) {
        requestAnimationFrame(animate);
      } else {
        // Remove particles when animation completes
        scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animate();
  }

  /**
   * Create a special visual effect for the transformation
   */
  private createTransformationEffect(): void {
    const position = this.getPosition();
    // Get the scene from the mesh's parent
    const scene = this.getMesh().parent as THREE.Scene;

    // Create particles for transformation effect
    const particleCount = 30;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Initialize particles in a sphere around the enemy
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      const radius = 1;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particlePositions[i3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i3 + 2] = position.z + radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Create orange glowing particles
    const particleMaterial = new THREE.PointsMaterial({
      color: OrangeEnemy.ORANGE_COLOR,
      size: 0.3,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);

    // Play transformation sound
    audioManager.playSoundEffect('powerup');

    // Animate particles converging inward
    let time = 0;
    const duration = 1.5; // seconds

    const animate = () => {
      time += 0.016; // Approximately 60fps

      // Scale particles inward
      const scale = 1 - (time / duration);
      particles.scale.set(scale, scale, scale);

      // Increase brightness as they converge
      particleMaterial.opacity = 0.8 + (time / duration) * 0.2;

      if (time < duration) {
        requestAnimationFrame(animate);
      } else {
        // Remove particles when animation completes
        scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animate();
  }

  /**
   * Check if this enemy is the Orange enemy
   */
  isOrangeEnemy(): boolean {
    return true;
  }
}
