import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { SeededRandom } from '../utils/SeededRandom.js';
import { audioManager as AudioManager } from '../audio/AudioManager.js';

// Loot types for dungeon
export enum DungeonLootType {
  HEALTH_POTION = 'health_potion',
  MANA_POTION = 'mana_potion',
  EXPERIENCE_ORB = 'experience_orb',
  EQUIPMENT = 'equipment',
  GOLD = 'gold',
  RARE_MATERIAL = 'rare_material'
}

// Loot rarity levels
export enum LootRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

/**
 * DungeonLoot class for loot items in the dungeon
 */
export class DungeonLoot {
  private scene: THREE.Scene;
  private world: CANNON.World;
  private position: THREE.Vector3;
  private mesh: THREE.Group;
  private body: CANNON.Body;
  private type: DungeonLootType;
  private rarity: LootRarity;
  private dungeonLevel: number;
  private random: SeededRandom;
  private value: number;
  private collected: boolean = false;
  private collectionRange: number = 2; // Range at which player can collect
  
  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    dungeonLevel: number,
    seed: number
  ) {
    this.scene = scene;
    this.world = world;
    this.position = position;
    this.dungeonLevel = dungeonLevel;
    this.random = new SeededRandom(seed);
    
    // Determine loot type and rarity based on dungeon level and randomness
    this.type = this.determineLootType();
    this.rarity = this.determineLootRarity();
    
    // Set value based on type and rarity
    this.value = this.calculateValue();
    
    // Create visual representation
    this.mesh = this.createVisual();
    
    // Create physics body
    this.body = this.createPhysicsBody();
    
    // Add to scene and world
    this.scene.add(this.mesh);
    this.world.addBody(this.body);
  }
  
  /**
   * Determine loot type based on dungeon level and randomness
   */
  private determineLootType(): DungeonLootType {
    const roll = this.random.between(0, 1);
    
    if (roll < 0.3) {
      return DungeonLootType.GOLD;
    } else if (roll < 0.5) {
      return DungeonLootType.HEALTH_POTION;
    } else if (roll < 0.7) {
      return DungeonLootType.EXPERIENCE_ORB;
    } else if (roll < 0.85) {
      return DungeonLootType.EQUIPMENT;
    } else if (roll < 0.95) {
      return DungeonLootType.MANA_POTION;
    } else {
      return DungeonLootType.RARE_MATERIAL;
    }
  }
  
  /**
   * Determine loot rarity based on dungeon level and randomness
   */
  private determineLootRarity(): LootRarity {
    // Base probabilities
    const rarityProbabilities = {
      [LootRarity.COMMON]: 0.6,
      [LootRarity.UNCOMMON]: 0.25,
      [LootRarity.RARE]: 0.1,
      [LootRarity.EPIC]: 0.04,
      [LootRarity.LEGENDARY]: 0.01
    };
    
    // Adjust probabilities based on dungeon level
    // Higher levels have better chances for rare items
    const levelBonus = Math.min(0.5, this.dungeonLevel * 0.05);
    
    rarityProbabilities[LootRarity.COMMON] -= levelBonus * 0.6;
    rarityProbabilities[LootRarity.UNCOMMON] -= levelBonus * 0.2;
    rarityProbabilities[LootRarity.RARE] += levelBonus * 0.4;
    rarityProbabilities[LootRarity.EPIC] += levelBonus * 0.3;
    rarityProbabilities[LootRarity.LEGENDARY] += levelBonus * 0.1;
    
    // Roll for rarity
    const roll = this.random.between(0, 1);
    let cumulativeProbability = 0;
    
    for (const rarity of Object.values(LootRarity)) {
      cumulativeProbability += rarityProbabilities[rarity];
      if (roll < cumulativeProbability) {
        return rarity;
      }
    }
    
    // Fallback
    return LootRarity.COMMON;
  }
  
  /**
   * Calculate value based on type and rarity
   */
  private calculateValue(): number {
    // Base values by type
    let baseValue: number;
    
    switch (this.type) {
      case DungeonLootType.GOLD:
        baseValue = 10;
        break;
      case DungeonLootType.HEALTH_POTION:
        baseValue = 20;
        break;
      case DungeonLootType.MANA_POTION:
        baseValue = 25;
        break;
      case DungeonLootType.EXPERIENCE_ORB:
        baseValue = 30;
        break;
      case DungeonLootType.EQUIPMENT:
        baseValue = 50;
        break;
      case DungeonLootType.RARE_MATERIAL:
        baseValue = 100;
        break;
      default:
        baseValue = 10;
    }
    
    // Rarity multipliers
    const rarityMultiplier = {
      [LootRarity.COMMON]: 1,
      [LootRarity.UNCOMMON]: 2,
      [LootRarity.RARE]: 4,
      [LootRarity.EPIC]: 8,
      [LootRarity.LEGENDARY]: 16
    };
    
    // Apply dungeon level scaling (10% increase per level)
    const levelMultiplier = 1 + (this.dungeonLevel - 1) * 0.1;
    
    // Calculate final value
    return Math.ceil(baseValue * rarityMultiplier[this.rarity] * levelMultiplier);
  }
  
  /**
   * Create visual representation based on loot type and rarity
   */
  private createVisual(): THREE.Group {
    const group = new THREE.Group();
    
    // Base geometry and material based on loot type
    let geometry: THREE.BufferGeometry;
    let material: THREE.Material;
    let color: number;
    
    switch (this.type) {
      case DungeonLootType.GOLD:
        geometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1, 16);
        color = 0xFFD700; // Gold
        break;
      case DungeonLootType.HEALTH_POTION:
        geometry = new THREE.CapsuleGeometry(0.2, 0.4, 8, 8);
        color = 0xFF0000; // Red
        break;
      case DungeonLootType.MANA_POTION:
        geometry = new THREE.CapsuleGeometry(0.2, 0.4, 8, 8);
        color = 0x0000FF; // Blue
        break;
      case DungeonLootType.EXPERIENCE_ORB:
        geometry = new THREE.SphereGeometry(0.3, 16, 16);
        color = 0x00FF00; // Green
        break;
      case DungeonLootType.EQUIPMENT:
        geometry = new THREE.BoxGeometry(0.4, 0.4, 0.4);
        color = 0xAAAAAA; // Gray
        break;
      case DungeonLootType.RARE_MATERIAL:
        geometry = new THREE.OctahedronGeometry(0.3, 0);
        color = 0xFF00FF; // Purple
        break;
      default:
        geometry = new THREE.SphereGeometry(0.3, 16, 16);
        color = 0xFFFFFF; // White
    }
    
    // Adjust material based on rarity
    let emissiveIntensity = 0.2;
    
    switch (this.rarity) {
      case LootRarity.COMMON:
        // No adjustment
        break;
      case LootRarity.UNCOMMON:
        emissiveIntensity = 0.4;
        break;
      case LootRarity.RARE:
        emissiveIntensity = 0.6;
        break;
      case LootRarity.EPIC:
        emissiveIntensity = 0.8;
        break;
      case LootRarity.LEGENDARY:
        emissiveIntensity = 1.0;
        // Add special glow effect for legendary items
        const glowGeometry = new THREE.SphereGeometry(0.5, 16, 16);
        const glowMaterial = new THREE.MeshBasicMaterial({
          color: color,
          transparent: true,
          opacity: 0.3,
          side: THREE.BackSide
        });
        
        const glow = new THREE.Mesh(glowGeometry, glowMaterial);
        group.add(glow);
        break;
    }
    
    material = new THREE.MeshStandardMaterial({
      color: color,
      emissive: color,
      emissiveIntensity: emissiveIntensity,
      metalness: 0.8,
      roughness: 0.2
    });
    
    const mainMesh = new THREE.Mesh(geometry, material);
    group.add(mainMesh);
    
    // Add floating effect
    group.position.copy(this.position);
    group.position.y += 0.5; // Float above ground
    
    return group;
  }
  
  /**
   * Create physics body for the loot
   */
  private createPhysicsBody(): CANNON.Body {
    // Create a sphere shape for physics
    const radius = 0.3;
    const shape = new CANNON.Sphere(radius);
    
    // Create body
    const body = new CANNON.Body({
      mass: 1, // Light
      position: new CANNON.Vec3(this.position.x, this.position.y + 0.5, this.position.z),
      shape: shape,
      material: new CANNON.Material({
        friction: 0.3,
        restitution: 0.6
      }),
      linearDamping: 0.9, // Add damping to prevent excessive movement
      angularDamping: 0.9
    });
    
    return body;
  }
  
  /**
   * Update loot state
   */
  public update(delta: number): void {
    if (this.collected) return;
    
    // Update position from physics
    this.mesh.position.copy(this.body.position);
    
    // Add floating animation
    this.mesh.position.y += Math.sin(Date.now() * 0.002) * 0.05;
    this.mesh.rotation.y += delta * 0.5; // Rotate slowly
    
    // Add special effects for higher rarity items
    if (this.rarity === LootRarity.EPIC || this.rarity === LootRarity.LEGENDARY) {
      this.animateRareEffects(delta);
    }
  }
  
  /**
   * Animate special effects for rare items
   */
  private animateRareEffects(delta: number): void {
    // Find glow mesh if it exists
    this.mesh.traverse(child => {
      if (child instanceof THREE.Mesh && 
          child.material instanceof THREE.MeshBasicMaterial && 
          child.material.opacity < 0.5) { // Identify glow by low opacity
        
        // Pulse glow
        child.scale.x = 1.0 + Math.sin(Date.now() * 0.002) * 0.2;
        child.scale.y = 1.0 + Math.sin(Date.now() * 0.002) * 0.2;
        child.scale.z = 1.0 + Math.sin(Date.now() * 0.002) * 0.2;
      }
    });
  }
  
  /**
   * Check if player is in range to collect
   */
  public checkCollection(playerPosition: THREE.Vector3): boolean {
    if (this.collected) return false;
    
    const distance = this.mesh.position.distanceTo(playerPosition);
    
    if (distance < this.collectionRange) {
      this.collect();
      return true;
    }
    
    return false;
  }
  
  /**
   * Collect the loot
   */
  private collect(): void {
    if (this.collected) return;
    
    this.collected = true;
    
    // Play collection sound based on rarity
    switch (this.rarity) {
      case LootRarity.COMMON:
      case LootRarity.UNCOMMON:
        AudioManager.playSoundEffect('lootCollect');
        break;
      case LootRarity.RARE:
        AudioManager.playSoundEffect('rareLootCollect');
        break;
      case LootRarity.EPIC:
      case LootRarity.LEGENDARY:
        AudioManager.playSoundEffect('epicLootCollect');
        break;
    }
    
    // Create collection effect
    this.createCollectionEffect();
  }
  
  /**
   * Create visual effect for loot collection
   */
  private createCollectionEffect(): void {
    // Get color based on loot type
    let color: number;
    switch (this.type) {
      case DungeonLootType.GOLD: color = 0xFFD700; break;
      case DungeonLootType.HEALTH_POTION: color = 0xFF0000; break;
      case DungeonLootType.MANA_POTION: color = 0x0000FF; break;
      case DungeonLootType.EXPERIENCE_ORB: color = 0x00FF00; break;
      case DungeonLootType.EQUIPMENT: color = 0xAAAAAA; break;
      case DungeonLootType.RARE_MATERIAL: color = 0xFF00FF; break;
      default: color = 0xFFFFFF;
    }
    
    // Create particles
    const particleCount = 20;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    
    // Get loot position
    const position = this.mesh.position.clone();
    
    // Create particles in a sphere around loot
    for (let i = 0; i < particleCount; i++) {
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const radius = 0.2 + Math.random() * 0.3;
      
      particlePositions[i * 3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = position.z + radius * Math.cos(phi);
    }
    
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    
    const particleMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.1,
      transparent: true,
      opacity: 1.0
    });
    
    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);
    
    // Animate and remove particles
    let time = 0;
    const duration = 0.5;
    
    const animateParticles = () => {
      time += 0.016; // Approximately 60fps
      
      if (time < duration) {
        // Move particles outward
        const positions = particleGeometry.attributes.position.array;
        
        for (let i = 0; i < particleCount; i++) {
          const index = i * 3;
          const dirX = positions[index] - position.x;
          const dirY = positions[index + 1] - position.y;
          const dirZ = positions[index + 2] - position.z;
          
          positions[index] += dirX * 0.1;
          positions[index + 1] += dirY * 0.1;
          positions[index + 2] += dirZ * 0.1;
        }
        
        particleGeometry.attributes.position.needsUpdate = true;
        
        // Fade out
        particleMaterial.opacity = 1.0 * (1 - time / duration);
        
        requestAnimationFrame(animateParticles);
      } else {
        // Remove particles
        this.scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };
    
    animateParticles();
    
    // Hide loot mesh
    this.mesh.visible = false;
  }
  
  /**
   * Check if loot is collected
   */
  public isCollected(): boolean {
    return this.collected;
  }
  
  /**
   * Get loot data
   */
  public getLootData(): any {
    return {
      type: this.type,
      rarity: this.rarity,
      value: this.value,
      position: this.mesh.position.clone(),
      dungeonLevel: this.dungeonLevel
    };
  }
  
  /**
   * Get loot position
   */
  public getPosition(): THREE.Vector3 {
    return this.mesh.position.clone();
  }
  
  /**
   * Dispose of all resources
   */
  public dispose(): void {
    // Remove from scene
    this.scene.remove(this.mesh);
    
    // Remove from physics world
    this.world.removeBody(this.body);
    
    // Dispose of geometries and materials
    this.mesh.traverse(child => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) child.geometry.dispose();
        
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else if (child.material) {
          child.material.dispose();
        }
      }
    });
  }
}
