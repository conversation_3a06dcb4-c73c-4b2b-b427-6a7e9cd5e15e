import * as THREE from 'three';

/**
 * Options for creating particle effects
 */
interface ParticleEffectOptions {
  color?: number;
  count?: number;
  duration?: number;
  size?: number;
  speed?: number;
  radius?: number;
  direction?: THREE.Vector3;
}

/**
 * Manages particle effects in the game
 */
export class ParticleSystem {
  private scene: THREE.Scene;
  private activeEffects: Map<string, {
    particles: THREE.Points;
    startTime: number;
    duration: number;
  }> = new Map();

  constructor(scene: THREE.Scene) {
    this.scene = scene;
  }

  /**
   * Create a particle effect
   * @param type The type of effect to create
   * @param position The position of the effect
   * @param options Options for the effect
   */
  public createEffect(
    type: string,
    position: THREE.Vector3,
    options: ParticleEffectOptions = {}
  ): void {
    // Set default options
    const color = options.color || 0xffffff;
    const count = options.count || 20;
    const duration = options.duration || 1000;
    const size = options.size || 0.2;
    const speed = options.speed || 0.05;
    const radius = options.radius || 1;
    const direction = options.direction || new THREE.Vector3(0, 1, 0);

    // Create particles based on effect type
    let particles: THREE.Points;

    switch (type) {
      case 'spawn':
        particles = this.createSpawnEffect(position, color, count, size, radius);
        break;
      case 'attack':
        particles = this.createAttackEffect(position, color, count, size, direction, speed);
        break;
      case 'special':
        particles = this.createSpecialEffect(position, color, count, size, radius);
        break;
      case 'damage':
        particles = this.createDamageEffect(position, color, count, size, radius);
        break;
      case 'heal':
        particles = this.createHealEffect(position, color, count, size, radius);
        break;
      case 'victory':
        particles = this.createVictoryEffect(position, color, count, size, radius);
        break;
      case 'remove':
        particles = this.createRemoveEffect(position, color, count, size, radius);
        break;
      case 'devour':
        particles = this.createDevourEffect(position, color, count, size, radius, direction);
        break;
      default:
        particles = this.createDefaultEffect(position, color, count, size, radius);
    }

    // Add to scene
    this.scene.add(particles);

    // Store in active effects
    const effectId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.activeEffects.set(effectId, {
      particles,
      startTime: Date.now(),
      duration
    });

    // Set up cleanup
    setTimeout(() => {
      this.removeEffect(effectId);
    }, duration);

    // Start animation
    this.animateEffect(effectId);
  }

  /**
   * Create a spawn effect (particles expanding from center)
   */
  private createSpawnEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Start at center
      vertices.push(0, 0, 0);

      // Random direction
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const vx = Math.sin(phi) * Math.cos(theta);
      const vy = Math.sin(phi) * Math.sin(theta);
      const vz = Math.cos(phi);

      // Random speed
      const speed = 0.05 + Math.random() * 0.05;
      velocities.push(vx * speed, vy * speed, vz * speed);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create an attack effect (particles moving in a direction)
   */
  private createAttackEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    direction: THREE.Vector3,
    speed: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    // Normalize direction
    direction.normalize();

    for (let i = 0; i < count; i++) {
      // Start at center with small random offset
      vertices.push(
        (Math.random() - 0.5) * 0.5,
        (Math.random() - 0.5) * 0.5,
        (Math.random() - 0.5) * 0.5
      );

      // Direction with small random variation
      const vx = direction.x + (Math.random() - 0.5) * 0.3;
      const vy = direction.y + (Math.random() - 0.5) * 0.3;
      const vz = direction.z + (Math.random() - 0.5) * 0.3;

      // Random speed
      const particleSpeed = speed * (0.8 + Math.random() * 0.4);
      velocities.push(vx * particleSpeed, vy * particleSpeed, vz * particleSpeed);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a special effect (particles in a spiral)
   */
  private createSpecialEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Start in a sphere
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const r = radius * Math.random();

      const x = r * Math.sin(phi) * Math.cos(theta);
      const y = r * Math.sin(phi) * Math.sin(theta);
      const z = r * Math.cos(phi);

      vertices.push(x, y, z);

      // Spiral velocity
      const vx = y * 0.05;
      const vy = -x * 0.05;
      const vz = (Math.random() - 0.5) * 0.05;

      velocities.push(vx, vy, vz);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a damage effect (particles exploding outward)
   */
  private createDamageEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Start near center
      vertices.push(
        (Math.random() - 0.5) * 0.5,
        (Math.random() - 0.5) * 0.5,
        (Math.random() - 0.5) * 0.5
      );

      // Explode outward
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const vx = Math.sin(phi) * Math.cos(theta);
      const vy = Math.sin(phi) * Math.sin(theta);
      const vz = Math.cos(phi);

      // Random speed
      const speed = 0.1 + Math.random() * 0.1;
      velocities.push(vx * speed, vy * speed, vz * speed);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a heal effect (particles rising upward)
   */
  private createHealEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Start in a circle around the position
      const theta = Math.random() * Math.PI * 2;
      const r = radius * Math.random();

      const x = r * Math.cos(theta);
      const y = 0;
      const z = r * Math.sin(theta);

      vertices.push(x, y, z);

      // Rise upward with slight randomness
      const vx = (Math.random() - 0.5) * 0.02;
      const vy = 0.05 + Math.random() * 0.05;
      const vz = (Math.random() - 0.5) * 0.02;

      velocities.push(vx, vy, vz);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a victory effect (particles in a fountain)
   */
  private createVictoryEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Start near center
      vertices.push(
        (Math.random() - 0.5) * 0.5,
        0,
        (Math.random() - 0.5) * 0.5
      );

      // Fountain velocity
      const theta = Math.random() * Math.PI * 2;
      const vx = Math.cos(theta) * 0.05;
      const vy = 0.1 + Math.random() * 0.1;
      const vz = Math.sin(theta) * 0.05;

      velocities.push(vx, vy, vz);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;
    geometry.userData.gravity = -0.001; // Add gravity

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a remove effect (particles imploding)
   */
  private createRemoveEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Start in a sphere
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;
      const r = radius * (0.8 + Math.random() * 0.2);

      const x = r * Math.sin(phi) * Math.cos(theta);
      const y = r * Math.sin(phi) * Math.sin(theta);
      const z = r * Math.cos(phi);

      vertices.push(x, y, z);

      // Implode toward center
      const speed = 0.05 + Math.random() * 0.05;
      velocities.push(-x * speed, -y * speed, -z * speed);
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a devouring effect (particles being pulled toward a direction)
   */
  private createDevourEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number,
    direction: THREE.Vector3
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Random position within radius around the source
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * radius;
      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;
      const y = Math.random() * 2 - 1;

      vertices.push(x, y, z);

      // Velocity toward the direction (pet position)
      const pullStrength = 0.05 + Math.random() * 0.03;
      velocities.push(
        direction.x * pullStrength,
        direction.y * pullStrength,
        direction.z * pullStrength
      );
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material with orange color for devouring
    const material = new THREE.PointsMaterial({
      color: 0xff6600, // Orange color for devouring
      size: size * 1.2, // Slightly larger particles
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Create a default effect (simple particles)
   */
  private createDefaultEffect(
    position: THREE.Vector3,
    color: number,
    count: number,
    size: number,
    radius: number
  ): THREE.Points {
    const geometry = new THREE.BufferGeometry();
    const vertices = [];
    const velocities = [];

    for (let i = 0; i < count; i++) {
      // Random position within radius
      vertices.push(
        (Math.random() - 0.5) * radius,
        (Math.random() - 0.5) * radius,
        (Math.random() - 0.5) * radius
      );

      // Random velocity
      velocities.push(
        (Math.random() - 0.5) * 0.05,
        (Math.random() - 0.5) * 0.05,
        (Math.random() - 0.5) * 0.05
      );
    }

    // Create geometry
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.userData.velocities = velocities;

    // Create material
    const material = new THREE.PointsMaterial({
      color,
      size,
      transparent: true,
      opacity: 1,
      blending: THREE.AdditiveBlending
    });

    // Create points
    const points = new THREE.Points(geometry, material);
    points.position.copy(position);

    return points;
  }

  /**
   * Animate a particle effect
   */
  private animateEffect(effectId: string): void {
    const effect = this.activeEffects.get(effectId);
    if (!effect) return;

    const { particles, startTime, duration } = effect;
    const elapsed = Date.now() - startTime;
    const progress = elapsed / duration;

    // Skip if effect is done
    if (progress >= 1) return;

    // Get positions and velocities
    const positions = particles.geometry.getAttribute('position');
    const velocities = particles.geometry.userData.velocities;
    const gravity = particles.geometry.userData.gravity || 0;

    // Update positions
    for (let i = 0; i < positions.count; i++) {
      // Get current position
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      // Get velocity
      const vx = velocities[i * 3];
      const vy = velocities[i * 3 + 1];
      const vz = velocities[i * 3 + 2];

      // Update position
      positions.setX(i, x + vx);
      positions.setY(i, y + vy + gravity); // Apply gravity if set
      positions.setZ(i, z + vz);
    }

    // Mark positions for update
    positions.needsUpdate = true;

    // Update opacity based on progress
    if (progress > 0.7) {
      const fadeProgress = (progress - 0.7) / 0.3;
      if (Array.isArray(particles.material)) {
        particles.material.forEach(material => {
          if ('opacity' in material) {
            (material as any).opacity = 1 - fadeProgress;
          }
        });
      } else if ('opacity' in particles.material) {
        (particles.material as any).opacity = 1 - fadeProgress;
      }
    }

    // Request next frame
    requestAnimationFrame(() => this.animateEffect(effectId));
  }

  /**
   * Remove a particle effect
   */
  private removeEffect(effectId: string): void {
    const effect = this.activeEffects.get(effectId);
    if (!effect) return;

    // Remove from scene
    this.scene.remove(effect.particles);

    // Dispose resources
    effect.particles.geometry.dispose();
    if (effect.particles.material instanceof THREE.Material) {
      effect.particles.material.dispose();
    } else if (Array.isArray(effect.particles.material)) {
      effect.particles.material.forEach(material => material.dispose());
    }

    // Remove from active effects
    this.activeEffects.delete(effectId);
  }

  /**
   * Update all active effects
   * This should be called in the game loop if not using requestAnimationFrame
   */
  public update(): void {
    const now = Date.now();

    // Check for expired effects
    for (const [effectId, effect] of this.activeEffects.entries()) {
      if (now - effect.startTime >= effect.duration) {
        this.removeEffect(effectId);
      }
    }
  }

  /**
   * Clear all effects
   */
  public clear(): void {
    for (const [effectId, effect] of this.activeEffects.entries()) {
      this.scene.remove(effect.particles);
      effect.particles.geometry.dispose();
      if (effect.particles.material instanceof THREE.Material) {
        effect.particles.material.dispose();
      } else if (Array.isArray(effect.particles.material)) {
        effect.particles.material.forEach(material => material.dispose());
      }
    }

    this.activeEffects.clear();
  }
}
