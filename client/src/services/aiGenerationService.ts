// Base API URL
const API_BASE_URL = '/api';
import { LLMService } from './llmService.js';

/**
 * Service for generating AI images for pet specters
 */
export class AIGenerationService {
  // Cache for generated images to avoid redundant API calls
  private static imageCache: Map<string, string> = new Map();

  /**
   * Generate a pet specter image based on user preferences
   */
  static async generatePetSpecterImage(
    preferences: {
      specterType: string;
      description: string;
      color?: string;
      style?: string;
    },
    options: {
      bypassCache?: boolean;
    } = {}
  ): Promise<{ imageUrl: string; prompt: string; requestId?: string; queuePosition?: number }> {
    try {
      // Create a cache key based on preferences
      const cacheKey = this.createCacheKey(preferences);

      // Check if image is already in cache (unless bypass is requested)
      if (!options.bypassCache) {
        const cachedImage = this.imageCache.get(cacheKey);
        if (cachedImage) {
          console.log('Using cached image');
          return {
            imageUrl: cachedImage,
            prompt: preferences.description
          };
        }
      } else {
        console.log('Bypassing cache as requested');
      }

      try {
        // Enhance the prompt using LLM - this may return queue info
        const enhancedPrompt = await LLMService.enhancePromptForPetGeneration(preferences);

        // Call the API to generate the image
        console.log('Sending enhanced prompt to server:', enhancedPrompt);
        const response = await fetch(`${API_BASE_URL}/ai/generate-image`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: enhancedPrompt,
            specterType: preferences.specterType,
            color: preferences.color,
            style: preferences.style,
            bypassCache: options.bypassCache
          }),
        });

        // Check if the request is queued
        if (response.status === 202) {
          const queueData = await response.json();
          console.log('Image generation request queued:', queueData);

          return {
            imageUrl: '',
            prompt: preferences.description,
            requestId: queueData.requestId,
            queuePosition: queueData.queuePosition
          };
        }

        if (!response.ok) {
          throw new Error(`Failed to generate image: ${response.statusText}`);
        }

        const result = await response.json();

        // Cache the result
        this.imageCache.set(cacheKey, result.imageUrl);

        return {
          imageUrl: result.imageUrl,
          prompt: enhancedPrompt
        };
      } catch (error) {
        // If the error contains queue information, return it
        if (error instanceof Error && error.message.includes('queued')) {
          const match = error.message.match(/requestId: ([\w-]+), queuePosition: (\d+)/);
          if (match) {
            return {
              imageUrl: '',
              prompt: preferences.description,
              requestId: match[1],
              queuePosition: parseInt(match[2], 10)
            };
          }
        }
        throw error;
      }
    } catch (error) {
      console.error('Error generating pet specter image:', error);
      throw error;
    }
  }

  /**
   * Generate a pet specter based on an existing NFT image
   */
  static async generatePetFromNFTImage(
    nftImageUrl: string
  ): Promise<{
    imageUrl: string;
    analysis?: {
      recommendedType: string;
      colorPalette: string[];
      description: string;
      style: string;
    };
    requestId?: string;
    queuePosition?: number;
  }> {
    try {
      // Create a cache key based on the NFT image URL
      const cacheKey = `nft-${nftImageUrl}`;

      // Check if result is already in cache
      const cachedResult = this.imageCache.get(cacheKey);
      if (cachedResult) {
        // Parse the cached result which contains both image URL and analysis
        const parsedResult = JSON.parse(cachedResult);
        return parsedResult;
      }

      try {
        // First analyze the NFT image - this may return queue info
        const analysisResult = await LLMService.analyzeNFTImage(nftImageUrl);

        // Check if the analysis is queued
        if ('requestId' in analysisResult && 'queuePosition' in analysisResult) {
          return {
            imageUrl: '',
            requestId: analysisResult.requestId,
            queuePosition: analysisResult.queuePosition
          };
        }

        // If we have the analysis, generate the image
        const analysis = analysisResult;

        // Then generate a pet specter based on the analysis
        const { imageUrl, requestId, queuePosition } = await this.generatePetSpecterImage({
          specterType: analysis.recommendedType,
          description: analysis.description,
          color: analysis.colorPalette[0],
          style: analysis.style
        });

        // Check if the image generation is queued
        if (requestId && queuePosition) {
          return {
            imageUrl: '',
            analysis,
            requestId,
            queuePosition
          };
        }

        const result = {
          imageUrl,
          analysis
        };

        // Cache the result as a JSON string
        this.imageCache.set(cacheKey, JSON.stringify(result));

        return result;
      } catch (error) {
        // If the error contains queue information, return it
        if (error instanceof Error && error.message.includes('queued')) {
          const match = error.message.match(/requestId: ([\w-]+), queuePosition: (\d+)/);
          if (match) {
            return {
              imageUrl: '',
              requestId: match[1],
              queuePosition: parseInt(match[2], 10)
            };
          }
        }
        throw error;
      }
    } catch (error) {
      console.error('Error generating pet from NFT image:', error);
      throw error;
    }
  }

  /**
   * Create a cache key based on preferences
   */
  private static createCacheKey(preferences: {
    specterType: string;
    description: string;
    color?: string;
    style?: string;
  }): string {
    return `${preferences.specterType}-${preferences.description}-${preferences.color || ''}-${preferences.style || ''}`;
  }

  /**
   * Clear the image cache
   */
  static clearCache(): void {
    this.imageCache.clear();
  }

  /**
   * Clear a specific entry from the cache
   */
  static clearCacheForPreferences(preferences: {
    specterType: string;
    description: string;
    color?: string;
    style?: string;
  }): void {
    const cacheKey = this.createCacheKey(preferences);
    this.imageCache.delete(cacheKey);
  }
}
