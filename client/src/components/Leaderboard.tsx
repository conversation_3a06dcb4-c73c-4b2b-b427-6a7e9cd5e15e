import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Badge } from '@/components/ui/badge.jsx';
import { Loader2, Trophy, Medal, Award, Crown } from 'lucide-react';
import { GameMode } from '@shared/schema.js';

interface HighScore {
  id: number;
  playerName: string;
  score: number;
  gameMode: string;
  teamName?: string;
  createdAt: string;
}

interface LeaderboardProps {
  isOpen: boolean;
  onClose: () => void;
}

const Leaderboard: React.FC<LeaderboardProps> = ({ isOpen, onClose }) => {
  const [scores, setScores] = useState<HighScore[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMode, setSelectedMode] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // Fetch scores from API
  const fetchScores = async (gameMode?: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (gameMode && gameMode !== 'all') {
        params.append('gameMode', gameMode);
      }
      params.append('limit', '50'); // Get top 50 scores
      
      const response = await fetch(`/api/scores?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch leaderboard');
      }
      
      const data = await response.json();
      setScores(data);
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
      setError('Failed to load leaderboard. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch scores when dialog opens or mode changes
  useEffect(() => {
    if (isOpen) {
      fetchScores(selectedMode);
    }
  }, [isOpen, selectedMode]);

  // Get rank icon based on position
  const getRankIcon = (position: number) => {
    switch (position) {
      case 1:
        return <Crown className="h-5 w-5 text-yellow-400" />;
      case 2:
        return <Trophy className="h-5 w-5 text-gray-300" />;
      case 3:
        return <Medal className="h-5 w-5 text-amber-600" />;
      default:
        return <Award className="h-5 w-5 text-blue-400" />;
    }
  };

  // Get rank styling based on position
  const getRankStyling = (position: number) => {
    switch (position) {
      case 1:
        return 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-yellow-400/30';
      case 2:
        return 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-gray-300/30';
      case 3:
        return 'bg-gradient-to-r from-amber-500/20 to-amber-600/20 border-amber-600/30';
      default:
        return 'bg-gray-800/50 border-gray-700/50';
    }
  };

  // Format game mode for display
  const formatGameMode = (mode: string) => {
    switch (mode) {
      case 'single':
        return 'Single Player';
      case GameMode.CoOp:
        return 'Co-op';
      case GameMode.PvP:
        return 'PvP';
      default:
        return mode;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl bg-gray-900 border-blue-400 text-white max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="title-font text-2xl text-blue-400 flex items-center gap-2">
            <Trophy className="h-6 w-6" />
            LEADERBOARD
          </DialogTitle>
        </DialogHeader>

        {/* Game Mode Filter */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={selectedMode === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMode('all')}
            className={selectedMode === 'all' ? 'bg-blue-600 hover:bg-blue-700' : ''}
          >
            All Modes
          </Button>
          <Button
            variant={selectedMode === 'single' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMode('single')}
            className={selectedMode === 'single' ? 'bg-blue-600 hover:bg-blue-700' : ''}
          >
            Single Player
          </Button>
          <Button
            variant={selectedMode === GameMode.CoOp ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMode(GameMode.CoOp)}
            className={selectedMode === GameMode.CoOp ? 'bg-blue-600 hover:bg-blue-700' : ''}
          >
            Co-op
          </Button>
          <Button
            variant={selectedMode === GameMode.PvP ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMode(GameMode.PvP)}
            className={selectedMode === GameMode.PvP ? 'bg-blue-600 hover:bg-blue-700' : ''}
          >
            PvP
          </Button>
        </div>

        {/* Scores List */}
        <div className="flex-1 overflow-y-auto pr-2">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
              <span className="ml-2 text-gray-300">Loading leaderboard...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-400 mb-4">{error}</p>
              <Button onClick={() => fetchScores(selectedMode)} variant="outline">
                Try Again
              </Button>
            </div>
          ) : scores.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Trophy className="h-12 w-12 mx-auto mb-4 opacity-30" />
              <p>No scores yet for this mode.</p>
              <p className="text-sm mt-1">Be the first to set a record!</p>
            </div>
          ) : (
            <div className="space-y-2">
              {scores.map((score, index) => {
                const position = index + 1;
                return (
                  <div
                    key={score.id}
                    className={`
                      flex items-center justify-between p-3 rounded-lg border transition-all
                      ${getRankStyling(position)}
                      hover:bg-opacity-80
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8">
                        {getRankIcon(position)}
                      </div>
                      <div>
                        <div className="font-semibold text-white">
                          {score.playerName}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {formatGameMode(score.gameMode)}
                          </Badge>
                          {score.teamName && (
                            <span className="text-blue-300">Team: {score.teamName}</span>
                          )}
                          <span>{formatDate(score.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold text-yellow-300">
                        {score.score.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-400">
                        #{position}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-700">
          <div className="text-xs text-gray-400">
            Showing top {scores.length} scores
          </div>
          <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default Leaderboard;
