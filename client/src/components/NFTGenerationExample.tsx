import React, { useState } from 'react';
import { AIGenerationService } from '../services/aiGenerationService.js';
import QueueStatusIndicator from './QueueStatusIndicator.jsx';
import { Button } from './ui/button.jsx';
import { Input } from './ui/input.jsx';
import { Textarea } from './ui/textarea.jsx';
import { Label } from './ui/label.jsx';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card.jsx';

/**
 * Example component for NFT generation with queue status
 */
const NFTGenerationExample: React.FC = () => {
  const [description, setDescription] = useState<string>('');
  const [specterType, setSpecterType] = useState<string>('WISP');
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Queue status
  const [requestId, setRequestId] = useState<string | null>(null);
  const [queuePosition, setQueuePosition] = useState<number>(0);

  const handleGenerate = async () => {
    if (!description) {
      setError('Please enter a description');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await AIGenerationService.generatePetSpecterImage({
        specterType,
        description
      });

      // Check if the request is queued
      if (result.requestId && result.queuePosition) {
        setRequestId(result.requestId);
        setQueuePosition(result.queuePosition);
      } else {
        // If not queued, we have the image
        setImageUrl(result.imageUrl);
      }
    } catch (error) {
      console.error('Error generating image:', error);
      setError('Failed to generate image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle completion of queued request
  const handleQueueComplete = async () => {
    setIsLoading(true);

    try {
      // Fetch the image again now that it's ready
      const result = await AIGenerationService.generatePetSpecterImage({
        specterType,
        description
      });

      setImageUrl(result.imageUrl);

      // Reset queue status
      setRequestId(null);
      setQueuePosition(0);
    } catch (error) {
      console.error('Error fetching completed image:', error);
      setError('Failed to fetch the generated image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Generate Pet Specter</CardTitle>
        <CardDescription>
          Enter a description to generate a unique pet specter
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="specterType">Specter Type</Label>
            <select
              id="specterType"
              value={specterType}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setSpecterType(e.target.value)}
              className="w-full p-2 border rounded"
              disabled={isLoading}
            >
              <option value="WISP">Wisp</option>
              <option value="PHANTOM">Phantom</option>
              <option value="WRAITH">Wraith</option>
              <option value="POLTERGEIST">Poltergeist</option>
              <option value="BANSHEE">Banshee</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setDescription(e.target.value)}
              placeholder="Describe your pet specter..."
              disabled={isLoading}
              className="min-h-[100px]"
            />
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
              {error}
            </div>
          )}

          <QueueStatusIndicator
            requestId={requestId}
            queuePosition={queuePosition}
            onComplete={handleQueueComplete}
          />

          {imageUrl && (
            <div className="mt-4">
              <img
                src={imageUrl}
                alt="Generated Pet Specter"
                className="w-full h-auto rounded-md border"
              />
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter>
        <Button
          onClick={handleGenerate}
          disabled={isLoading || !description}
          className="w-full"
        >
          {isLoading ? 'Generating...' : 'Generate Pet Specter'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default NFTGenerationExample;
