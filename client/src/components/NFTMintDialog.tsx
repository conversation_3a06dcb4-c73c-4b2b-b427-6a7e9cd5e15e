import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useWeb3 } from '@/contexts/Web3Context';
import { useToast } from '@/hooks/use-toast';
import { PetService } from '@/services/petService';
import { Loader2 } from 'lucide-react';
import { SpecterType, SpecterTier } from '@/game/types';

interface NFTMintDialogProps {
  isOpen: boolean;
  onClose: () => void;
  specterType: SpecterType;
  specterName: string;
  onMintSuccess: (tokenId: string) => void;
}

const NFTMintDialog: React.FC<NFTMintDialogProps> = ({
  isOpen,
  onClose,
  specterType,
  specterName,
  onMintSuccess,
}) => {
  const { isConnected, account, mintNFT, connectWallet } = useWeb3();
  const { toast } = useToast();
  const [isMinting, setIsMinting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  // Define the price based on specter tier
  const getPrice = () => {
    // Use the maticPrice if it's defined in the specterType
    if (specterType.maticPrice) {
      return specterType.maticPrice;
    }

    // Otherwise, determine price based on tier or name
    if (specterType.tier) {
      switch (specterType.tier) {
        case SpecterTier.COMMON: // WISP
          return '0.001'; // ~$1
        case SpecterTier.UNCOMMON: // PHANTOM
          return '0.002'; // ~$2
        case SpecterTier.RARE: // POLTERGEIST
          return '0.003'; // ~$3
        case SpecterTier.EPIC: // WRAITH
          return '0.005'; // ~$5
        case SpecterTier.LEGENDARY: // BANSHEE
          return '0.01'; // ~$10
        default:
          return '0.001';
      }
    } else {
      // Fallback to name-based pricing if tier is not set
      switch (specterType.name) {
        case 'WISP':
          return '0.001';
        case 'PHANTOM':
          return '0.005';
        case 'WRAITH':
          return '0.01';
        case 'POLTERGEIST':
          return '0.02';
        case 'BANSHEE':
          return '0.05';
        default:
          return '0.001';
      }
    }
  };

  const price = getPrice();

  // Calculate platform fee (3%)
  const platformFee = parseFloat(price) * 0.03;
  const totalCost = parseFloat(price) + platformFee;

  // Handle mint button click
  const handleMint = async () => {
    if (!isConnected) {
      setIsConnecting(true);
      await connectWallet();
      setIsConnecting(false);
      return;
    }

    setIsMinting(true);

    try {
      // Create metadata for the NFT
      const metadata = {
        name: specterName,
        description: `A ${specterType.tier ? `${specterType.tier} Tier ` : ''}${specterType.name} Pet Specter from Specter Shift game`,
        image: specterType.texture, // This should be a URL to the image
        attributes: [
          {
            trait_type: 'Type',
            value: specterType.name
          },
          {
            trait_type: 'Tier',
            value: specterType.tier || 'COMMON'
          },
          {
            trait_type: 'Color',
            value: specterType.color
          },
          {
            trait_type: 'Level',
            value: '1'
          },
          {
            trait_type: 'XP',
            value: '0'
          },
          {
            trait_type: 'Price',
            value: price
          }
        ]
      };

      // In a real implementation, you would upload this metadata to IPFS
      // For this example, we'll just stringify it
      const tokenURI = JSON.stringify(metadata);

      // Generate a unique game ID for the pet
      const gameId = `pet-${Date.now()}`;

      // Create pet data for database
      const petData = {
        gameId,
        name: specterName,
        specterType: specterType.name,
        walletAddress: account!,
        level: 1,
        xp: 0,
        traits: [
          { type: 'ATTACK', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'DEFENSE', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'SPEED', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'INTELLIGENCE', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'LOYALTY', level: 1, xp: 0, xpToNextLevel: 100 }
        ],
        stats: {
          health: 100,
          maxHealth: 100,
          attackPower: 10,
          defenseValue: 5,
          speed: 3
        }
      };

      try {
        // First create the pet in the database
        await PetService.createPetSpecter(petData);

        // Then mint the NFT
        const tokenId = await mintNFT(tokenURI, price, gameId);

        if (tokenId) {
          // Link the pet to the NFT
          await PetService.linkPetSpecterToNFT(gameId, tokenId, account!);

          // Call the success callback
          onMintSuccess(tokenId);
        }
      } catch (error) {
        console.error('Error creating pet or minting NFT:', error);
        toast({
          title: 'Error',
          description: 'Failed to create pet or mint NFT. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error minting NFT:', error);
    } finally {
      setIsMinting(false);
      // Call onClose to close the dialog and resume the game
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Mint Pet Specter as NFT</DialogTitle>
          <DialogDescription>
            Mint your {specterType.tier ? `${specterType.tier} Tier` : ''} {specterType.name} Pet Specter as an NFT on the Polygon Amoy Testnet.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex items-center justify-center mb-4">
            <div
              className="w-24 h-24 rounded-full"
              style={{
                backgroundColor: specterType.color,
                boxShadow: `0 0 15px ${specterType.color}`
              }}
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={specterName}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="type" className="text-right">
              Type
            </Label>
            <Input
              id="type"
              value={specterType.name}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="price" className="text-right">
              Price
            </Label>
            <Input
              id="price"
              value={`${price} MATIC`}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="fee" className="text-right">
              Platform Fee (3%)
            </Label>
            <Input
              id="fee"
              value={`${platformFee.toFixed(6)} MATIC`}
              className="col-span-3"
              disabled
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="total" className="text-right font-bold">
              Total
            </Label>
            <Input
              id="total"
              value={`${totalCost.toFixed(6)} MATIC`}
              className="col-span-3 font-bold"
              disabled
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleMint} disabled={isMinting || isConnecting}>
            {isMinting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Minting...
              </>
            ) : isConnecting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connecting...
              </>
            ) : !isConnected ? (
              'Connect Wallet to Mint'
            ) : (
              'Mint NFT'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NFTMintDialog;
