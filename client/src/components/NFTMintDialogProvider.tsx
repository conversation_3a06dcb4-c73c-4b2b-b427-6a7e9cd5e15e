import React, { useEffect, useState } from 'react';
import NFTMintDialog from './NFTMintDialog.jsx';
import { SpecterType } from '@/game/types';

/**
 * Provider component that listens for NFT minting events and shows the NFT mint dialog
 */
const NFTMintDialogProvider: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [specterType, setSpecterType] = useState<SpecterType | null>(null);
  const [specterName, setSpecterName] = useState('');
  const [onMintSuccess, setOnMintSuccess] = useState<(tokenId: string) => void>(() => () => {});
  const [gameEngine, setGameEngine] = useState<any>(null);

  useEffect(() => {
    // Listen for the showNFTMintDialog event
    const handleShowNFTMintDialog = (event: CustomEvent) => {
      const { isOpen, specterType, specterName, onMintSuccess, gameEngine } = event.detail;

      // Make sure the game is paused before showing the dialog
      if (gameEngine && typeof gameEngine.pause === 'function') {
        gameEngine.pause();
      }

      // Dispatch an event to notify the Game component that the NFT mint dialog is open
      const nftMintDialogOpenedEvent = new CustomEvent('nftMintDialogOpened');
      document.dispatchEvent(nftMintDialogOpenedEvent);

      setIsOpen(isOpen);
      setSpecterType(specterType);
      setSpecterName(specterName);
      setOnMintSuccess(() => onMintSuccess);
      setGameEngine(gameEngine);
    };

    // Add event listener
    document.addEventListener('showNFTMintDialog', handleShowNFTMintDialog as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('showNFTMintDialog', handleShowNFTMintDialog as EventListener);
    };
  }, []);

  // Handle dialog close
  const handleClose = () => {
    setIsOpen(false);

    // Dispatch an event to notify the Game component that the NFT mint dialog is closed
    const nftMintDialogClosedEvent = new CustomEvent('nftMintDialogClosed');
    document.dispatchEvent(nftMintDialogClosedEvent);

    // Let the Game component handle resuming the game
    // This ensures proper synchronization with the game's pause state
  };

  // Only render the dialog if we have all the necessary props
  if (!specterType) {
    return null;
  }

  return (
    <NFTMintDialog
      isOpen={isOpen}
      onClose={handleClose}
      specterType={specterType}
      specterName={specterName}
      onMintSuccess={(tokenId) => {
        onMintSuccess(tokenId);
        // Don't close the dialog here, let the NFTMintDialog handle it
      }}
    />
  );
};

export default NFTMintDialogProvider;
