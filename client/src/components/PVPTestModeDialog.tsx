import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog.jsx';
import { Button } from '@/components/ui/button.jsx';
import { Input } from '@/components/ui/input.jsx';
import { Label } from '@/components/ui/label.jsx';
import { useToast } from '@/hooks/use-toast.js';
import { Loader2, Beaker } from 'lucide-react';
import { useLocation } from 'wouter';

interface PVPTestModeDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const PVPTestModeDialog: React.FC<PVPTestModeDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [pet1Name, setPet1Name] = useState('Test Pet 1');
  const [pet2Name, setPet2Name] = useState('Test Pet 2');
  const [pet1Level, setPet1Level] = useState(5);
  const [pet2Level, setPet2Level] = useState(5);
  const [enableLogging, setEnableLogging] = useState(true);

  const handleStartTest = () => {
    if (!pet1Name.trim() || !pet2Name.trim()) {
      toast({
        title: "Names Required",
        description: "Please enter names for both test pets.",
        variant: "destructive",
        duration: 3000
      });
      return;
    }

    setIsLoading(true);

    // Store test configuration in session storage
    sessionStorage.setItem('pvpTestMode', 'true');
    sessionStorage.setItem('pvpTestPet1Name', pet1Name);
    sessionStorage.setItem('pvpTestPet2Name', pet2Name);
    sessionStorage.setItem('pvpTestPet1Level', pet1Level.toString());
    sessionStorage.setItem('pvpTestPet2Level', pet2Level.toString());
    sessionStorage.setItem('pvpTestLogging', enableLogging.toString());
    sessionStorage.setItem('gameMode', 'pvp');

    // Simulate loading
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Test Mode Activated",
        description: "Starting PVP test battle in the Coliseum Arena.",
        duration: 3000
      });
      onClose();
      setLocation('/game');
    }, 1000);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Beaker className="mr-2 h-5 w-5 text-blue-400" />
            PVP Test Mode
          </DialogTitle>
          <DialogDescription>
            Create a simulated battle between two pet specters to test the PVP system.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pet1Name">Pet 1 Name</Label>
              <Input
                id="pet1Name"
                value={pet1Name}
                onChange={(e) => setPet1Name(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="pet1Level">Pet 1 Level</Label>
              <Input
                id="pet1Level"
                type="number"
                min="1"
                max="20"
                value={pet1Level}
                onChange={(e) => setPet1Level(parseInt(e.target.value))}
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pet2Name">Pet 2 Name</Label>
              <Input
                id="pet2Name"
                value={pet2Name}
                onChange={(e) => setPet2Name(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="pet2Level">Pet 2 Level</Label>
              <Input
                id="pet2Level"
                type="number"
                min="1"
                max="20"
                value={pet2Level}
                onChange={(e) => setPet2Level(parseInt(e.target.value))}
                className="mt-1"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="enableLogging"
              checked={enableLogging}
              onChange={(e) => setEnableLogging(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <Label htmlFor="enableLogging">Enable detailed battle logging</Label>
          </div>

          <div className="bg-blue-900/20 border border-blue-500/30 rounded-md p-3 text-sm text-blue-300">
            This test mode will create two simulated pet specters and have them battle in the Coliseum Arena.
            You will be able to observe the battle as a spectator.
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleStartTest} 
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Starting...
              </>
            ) : (
              'Start Test Battle'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PVPTestModeDialog;
