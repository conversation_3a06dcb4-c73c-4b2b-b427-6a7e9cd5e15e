import React, { ReactNode } from 'react';
import { Button } from './button.jsx';
import { motion } from 'framer-motion';

interface IconButtonProps {
  onClick: () => void;
  icon: ReactNode;
  'aria-label': string;
}

export const IconButton: React.FC<IconButtonProps> = ({ 
  onClick, 
  icon,
  'aria-label': ariaLabel
}) => {
  return (
    <motion.div
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
    >
      <Button
        onClick={onClick}
        aria-label={ariaLabel}
        className="w-10 h-10 rounded-full bg-gray-800 hover:bg-gray-700 text-blue-400 border border-blue-500 flex items-center justify-center"
      >
        {icon}
      </Button>
    </motion.div>
  );
};
