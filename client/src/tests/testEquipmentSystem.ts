/**
 * Test script for the pet equipment system
 * This script tests the functionality of the equipment purchasing and inventory system
 */

import { playerInventory } from '../game/inventory/PlayerInventory.js';
import { AVAILABLE_EQUIPMENT, getEquipmentPrice } from '../game/data/EquipmentData.js';

export function testEquipmentSystem() {
  console.log('=== Testing Equipment System ===');
  
  // Clear inventory for testing
  playerInventory.clear();
  console.log('Cleared inventory');
  
  // Check initial state
  const initialEquipment = playerInventory.getAllEquipment();
  console.log(`Initial equipment count: ${initialEquipment.length}`);
  
  // Add some equipment
  const testEquipment1 = AVAILABLE_EQUIPMENT[0]; // First weapon
  const testEquipment2 = AVAILABLE_EQUIPMENT[3]; // First armor
  
  console.log(`Adding equipment: ${testEquipment1.name} (${testEquipment1.type})`);
  playerInventory.addEquipment(testEquipment1);
  
  console.log(`Adding equipment: ${testEquipment2.name} (${testEquipment2.type})`);
  playerInventory.addEquipment(testEquipment2);
  
  // Check updated state
  const updatedEquipment = playerInventory.getAllEquipment();
  console.log(`Updated equipment count: ${updatedEquipment.length}`);
  
  // Check if equipment is in inventory
  console.log(`Has ${testEquipment1.name}? ${playerInventory.hasEquipment(testEquipment1.id)}`);
  console.log(`Has ${testEquipment2.name}? ${playerInventory.hasEquipment(testEquipment2.id)}`);
  
  // Get equipment by type
  const weapons = playerInventory.getEquipmentByType('weapon');
  console.log(`Weapons count: ${weapons.length}`);
  
  const armor = playerInventory.getEquipmentByType('armor');
  console.log(`Armor count: ${armor.length}`);
  
  // Test equipment prices
  console.log('Equipment prices:');
  AVAILABLE_EQUIPMENT.forEach(equipment => {
    const price = getEquipmentPrice(equipment);
    console.log(`${equipment.name} (${equipment.rarity} ${equipment.type}): ${price} points`);
  });
  
  console.log('=== Equipment System Test Complete ===');
}

// Run the test
testEquipmentSystem();
