npx tsc --noEmit --project .
client/src/__tests__/game/controls/MobileControls.test.tsx:46:54 - error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<Element | null>'.

46     expect(document.querySelector('.left-joystick')).toBeInTheDocument();
                                                        ~~~~~~~~~~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:47:55 - error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<Element | null>'.

47     expect(document.querySelector('.right-joystick')).toBeInTheDocument();
                                                         ~~~~~~~~~~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:50:38 - error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.

50     expect(screen.getByText('JUMP')).toBeInTheDocument();
                                        ~~~~~~~~~~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:51:39 - error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.

51     expect(screen.getByText('SHOOT')).toBeInTheDocument();
                                         ~~~~~~~~~~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:52:41 - error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.

52     expect(screen.getByText('GRAPPLE')).toBeInTheDocument();
                                           ~~~~~~~~~~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:235:32 - error TS2554: Expected 1 arguments, but got 0.

235     const onMethod = (nipplejs.create() as any).on;
                                   ~~~~~~

  node_modules/nipplejs/types/index.d.ts:381:21
    381     function create(options: JoystickManagerOptions): JoystickManager;
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/components/AIPetGenerationDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:11:23 - error TS2307: Cannot find module '@/components/ui/input' or its corresponding type declarations.

11 import { Input } from '@/components/ui/input';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:12:23 - error TS2307: Cannot find module '@/components/ui/label' or its corresponding type declarations.

12 import { Label } from '@/components/ui/label';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:13:26 - error TS2307: Cannot find module '@/components/ui/textarea' or its corresponding type declarations.

13 import { Textarea } from '@/components/ui/textarea';
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:14:79 - error TS2307: Cannot find module '@/components/ui/select' or its corresponding type declarations.

14 import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
                                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:15:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

15 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:17:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

17 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:18:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

18 import { PetService } from '@/services/petService';
                              ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:19:37 - error TS2307: Cannot find module '@/services/aiGenerationService' or its corresponding type declarations.

19 import { AIGenerationService } from '@/services/aiGenerationService';
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AuthProtection.tsx:3:25 - error TS2307: Cannot find module '@/contexts/AuthContext' or its corresponding type declarations.

3 import { useAuth } from '@/contexts/AuthContext';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:2:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

2 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:3:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

3 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:4:25 - error TS2307: Cannot find module '@/contexts/AuthContext' or its corresponding type declarations.

4 import { useAuth } from '@/contexts/AuthContext';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:6:74 - error TS2307: Cannot find module '@/components/ui/tooltip' or its corresponding type declarations.

6 import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:11:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

11 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:13:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

13 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:14:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

14 import { PetService } from '@/services/petService';
                              ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:15:37 - error TS2307: Cannot find module '@/services/petGeneratorService' or its corresponding type declarations.

15 import { PetGeneratorService } from '@/services/petGeneratorService';
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:2:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

2 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:3:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

3 import { useToast } from '@/hooks/use-toast';
                           ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:12:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

12 } from '@/components/ui/dialog';
          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:13:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

13 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:153:47 - error TS2339: Property 'balanceOf' does not exist on type 'Contract'.

153             const balance = await nftContract.balanceOf(account);
                                                  ~~~~~~~~~

client/src/components/NFTBrowser.tsx:167:40 - error TS2339: Property 'name' does not exist on type 'Contract'.

167               name = await nftContract.name();
                                           ~~~~

client/src/components/NFTBrowser.tsx:168:42 - error TS2339: Property 'symbol' does not exist on type 'Contract'.

168               symbol = await nftContract.symbol();
                                             ~~~~~~

client/src/components/NFTBrowser.tsx:178:51 - error TS2339: Property 'tokenOfOwnerByIndex' does not exist on type 'Contract'.

178                 const tokenId = await nftContract.tokenOfOwnerByIndex(account, i);
                                                      ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:184:48 - error TS2339: Property 'tokenURI' does not exist on type 'Contract'.

184                   tokenURI = await nftContract.tokenURI(tokenId);
                                                   ~~~~~~~~

client/src/components/NFTMintDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:11:23 - error TS2307: Cannot find module '@/components/ui/input' or its corresponding type declarations.

11 import { Input } from '@/components/ui/input';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:12:23 - error TS2307: Cannot find module '@/components/ui/label' or its corresponding type declarations.

12 import { Label } from '@/components/ui/label';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:13:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

13 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:14:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

14 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:15:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

15 import { PetService } from '@/services/petService';
                              ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:17:42 - error TS2307: Cannot find module '@/game/types' or its corresponding type declarations.

17 import { SpecterType, SpecterTier } from '@/game/types';
                                            ~~~~~~~~~~~~~~

client/src/components/NFTMintDialogProvider.tsx:3:29 - error TS2307: Cannot find module '@/game/types' or its corresponding type declarations.

3 import { SpecterType } from '@/game/types';
                              ~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:7:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

7 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:9:32 - error TS2307: Cannot find module '@/hooks/useMultiplayer' or its corresponding type declarations.

9 import { useMultiplayer } from '@/hooks/useMultiplayer';
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:10:38 - error TS2307: Cannot find module '@/game/battle/TestTournamentBattle' or its corresponding type declarations.

10 import { TestTournamentBattle } from '@/game/battle/TestTournamentBattle';
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:11:35 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

11 import { RenderInstruction } from '@shared/schema';
                                     ~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:11:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

11 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:12:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

12 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:11:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

11 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:12:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

12 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:14:34 - error TS2307: Cannot find module '@/game/entities/PetSpecter' or its corresponding type declarations.

14 import { SpecterTraitType } from '@/game/entities/PetSpecter';
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/WalletConnectButton.tsx:2:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

2 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/WalletConnectButton.tsx:3:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

3 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/WalletConnectButton.tsx:5:74 - error TS2307: Cannot find module '@/components/ui/tooltip' or its corresponding type declarations.

5 import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/accordion.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/alert-dialog.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/alert-dialog.tsx:5:32 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

5 import { buttonVariants } from "@/components/ui/button"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/alert.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/avatar.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/badge.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/breadcrumb.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/button.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/calendar.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/calendar.tsx:6:32 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

6 import { buttonVariants } from "@/components/ui/button"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/card.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/carousel.tsx:7:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

7 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/carousel.tsx:8:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

8 import { Button } from "@/components/ui/button"
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/chart.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/checkbox.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/command.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/command.tsx:7:39 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

7 import { Dialog, DialogContent } from "@/components/ui/dialog"
                                        ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/context-menu.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/dialog.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/drawer.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/dropdown-menu.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/form.tsx:13:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

13 import { cn } from "@/lib/utils"
                      ~~~~~~~~~~~~~

client/src/components/ui/form.tsx:14:23 - error TS2307: Cannot find module '@/components/ui/label' or its corresponding type declarations.

14 import { Label } from "@/components/ui/label"
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/hover-card.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/input-otp.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/input.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/label.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/menubar.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/navigation-menu.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/pagination.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/pagination.tsx:5:45 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

5 import { ButtonProps, buttonVariants } from "@/components/ui/button"
                                              ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/popover.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/progress.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/radio-group.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/resizable.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/scroll-area.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/select.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/separator.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/sheet.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:6:29 - error TS2307: Cannot find module '@/hooks/use-mobile' or its corresponding type declarations.

6 import { useIsMobile } from "@/hooks/use-mobile"
                              ~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:7:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

7 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:8:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

8 import { Button } from "@/components/ui/button"
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:9:23 - error TS2307: Cannot find module '@/components/ui/input' or its corresponding type declarations.

9 import { Input } from "@/components/ui/input"
                        ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:10:27 - error TS2307: Cannot find module '@/components/ui/separator' or its corresponding type declarations.

10 import { Separator } from "@/components/ui/separator"
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:11:37 - error TS2307: Cannot find module '@/components/ui/sheet' or its corresponding type declarations.

11 import { Sheet, SheetContent } from "@/components/ui/sheet"
                                       ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:12:26 - error TS2307: Cannot find module '@/components/ui/skeleton' or its corresponding type declarations.

12 import { Skeleton } from "@/components/ui/skeleton"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:18:8 - error TS2307: Cannot find module '@/components/ui/tooltip' or its corresponding type declarations.

18 } from "@/components/ui/tooltip"
          ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/skeleton.tsx:1:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

1 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/slider.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/switch.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/table.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/tabs.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/textarea.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/toast.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/toaster.tsx:1:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

1 import { useToast } from "@/hooks/use-toast"
                           ~~~~~~~~~~~~~~~~~~~

client/src/components/ui/toaster.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/toast' or its corresponding type declarations.

9 } from "@/components/ui/toast"
         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/toggle-group.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/toggle-group.tsx:6:32 - error TS2307: Cannot find module '@/components/ui/toggle' or its corresponding type declarations.

6 import { toggleVariants } from "@/components/ui/toggle"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/toggle.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/tooltip.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/contexts/AuthContext.tsx:2:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

2 import { useWeb3 } from '@/contexts/Web3Context'; // Import useWeb3
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/contexts/PvpAccessContext.tsx:2:25 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './AuthContext.jsx'?

2 import { useAuth } from './AuthContext';
                          ~~~~~~~~~~~~~~~

client/src/contexts/Web3Context.tsx:4:33 - error TS2307: Cannot find module 'ethers/providers' or its corresponding type declarations.

4 import { BrowserProvider } from "ethers/providers";
                                  ~~~~~~~~~~~~~~~~~~

client/src/contexts/Web3Context.tsx:5:63 - error TS2307: Cannot find module 'ethers/utils' or its corresponding type declarations.

5 import { formatEther, parseEther, hexlify, toUtf8Bytes } from "ethers/utils";
                                                                ~~~~~~~~~~~~~~

client/src/contexts/Web3Context.tsx:6:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

6 import { useToast } from '@/hooks/use-toast';
                           ~~~~~~~~~~~~~~~~~~~

client/src/contexts/Web3Context.tsx:7:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

7 import { PetService } from '@/services/petService';
                             ~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/battle/TestTournamentBattle.ts:1:71 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

1 import { MessageType, RenderInstruction, RenderInstructionType } from '@shared/schema';
                                                                        ~~~~~~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:3:58 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

3 import { RenderInstruction, RenderInstructionType } from '@shared/schema';
                                                           ~~~~~~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:97:7 - error TS2345: Argument of type 'string' is not assignable to parameter of type 'Scene'.

97       String(petData.id),
         ~~~~~~~~~~~~~~~~~~

client/src/game/engine/InputHandler.ts:3:29 - error TS2307: Cannot find module '@/hooks/use-mobile' or its corresponding type declarations.

3 import { useIsMobile } from '@/hooks/use-mobile';
                              ~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:3:35 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../world/DungeonGenerator.js'?

3 import { DungeonDifficulty } from '../world/DungeonGenerator';
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:4:30 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../utils/SeededRandom.js'?

4 import { SeededRandom } from '../utils/SeededRandom';
                               ~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:5:46 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../audio/AudioManager.js'?

5 import { audioManager as AudioManager } from '../audio/AudioManager';
                                               ~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/pvp/PVPArenaManager.ts:5:45 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

5 import { MessageType, NetworkMessage } from '@shared/schema';
                                              ~~~~~~~~~~~~~~~~

client/src/game/ui/DungeonRewardsDialog.tsx:2:99 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

2 import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
                                                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/DungeonRewardsDialog.tsx:3:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

3 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/DungeonRewardsDialog.tsx:41:42 - error TS7006: Parameter 'open' implicitly has an 'any' type.

41     <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
                                            ~~~~

client/src/game/ui/PetSpecterUI.tsx:7:31 - error TS2307: Cannot find module '@/components/TrainingFeeDialog' or its corresponding type declarations.

7 import TrainingFeeDialog from '@/components/TrainingFeeDialog';
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/PetSpecterUI.tsx:8:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

8 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/PetSpecterUI.tsx:9:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

9 import { PetService } from '@/services/petService';
                             ~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/use-toast.ts:6:8 - error TS2307: Cannot find module '@/components/ui/toast' or its corresponding type declarations.

6 } from "@/components/ui/toast"
         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/use-toast.ts:158:22 - error TS7006: Parameter 'open' implicitly has an 'any' type.

158       onOpenChange: (open) => {
                         ~~~~

client/src/hooks/useMultiplayer.ts:2:87 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

2 import { NetworkMessage, MessageType, PlayerState, GameState, WeaponEffectData } from '@shared/schema';
                                                                                        ~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:2:45 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

2 import { MessageType, NetworkMessage } from '@shared/schema';
                                              ~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:3:85 - error TS2307: Cannot find module '@/utils/webSocketUtils' or its corresponding type declarations.

3 import { createWebSocketConnection, WebSocketConnectionType, getWebSocketUrl } from '@/utils/webSocketUtils';
                                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:4:124 - error TS2307: Cannot find module '@/utils/unifiedWebSocket' or its corresponding type declarations.

4 import { createTournamentBattleConnection, joinTournamentBattle as joinBattle, leaveTournamentBattle as leaveBattle } from '@/utils/unifiedWebSocket';
                                                                                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:291:21 - error TS7006: Parameter 'error' implicitly has an 'any' type.

291       ws.onerror = (error) => {
                        ~~~~~

client/src/hooks/useTournamentBattleConnection.ts:320:21 - error TS7006: Parameter 'event' implicitly has an 'any' type.

320       ws.onclose = (event) => {
                        ~~~~~

client/src/pages/AuthCallback.tsx:3:37 - error TS2307: Cannot find module '@/components/MinimalLoadingIndicator' or its corresponding type declarations.

3 import MinimalLoadingIndicator from '@/components/MinimalLoadingIndicator';
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:2:37 - error TS2307: Cannot find module '@/components/MinimalLoadingIndicator' or its corresponding type declarations.

2 import MinimalLoadingIndicator from '@/components/MinimalLoadingIndicator';
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:4:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

4 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:5:99 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

5 import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
                                                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:7:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

7 import { useToast } from '@/hooks/use-toast';
                           ~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:8:25 - error TS2307: Cannot find module '@/contexts/AuthContext' or its corresponding type declarations.

8 import { useAuth } from '@/contexts/AuthContext';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:11:32 - error TS2307: Cannot find module '@/hooks/useMultiplayer' or its corresponding type declarations.

11 import { useMultiplayer } from '@/hooks/useMultiplayer';
                                  ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:12:39 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

12 import { GameMode, PlayerState } from '@shared/schema';
                                         ~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:13:29 - error TS2307: Cannot find module '@/hooks/use-mobile' or its corresponding type declarations.

13 import { useIsMobile } from '@/hooks/use-mobile';
                               ~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:14:35 - error TS2307: Cannot find module '@/components/NFTMintDialogProvider' or its corresponding type declarations.

14 import NFTMintDialogProvider from '@/components/NFTMintDialogProvider';
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:15:39 - error TS2307: Cannot find module '@/components/NFTBasedPetDialogProvider' or its corresponding type declarations.

15 import NFTBasedPetDialogProvider from '@/components/NFTBasedPetDialogProvider';
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:16:43 - error TS2307: Cannot find module '@/components/AIPetGenerationDialogProvider' or its corresponding type declarations.

16 import AIPetGenerationDialogProvider from '@/components/AIPetGenerationDialogProvider';
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:17:38 - error TS2307: Cannot find module '@/components/NFTBrowserDialogProvider' or its corresponding type declarations.

17 import NFTBrowserDialogProvider from '@/components/NFTBrowserDialogProvider';
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:384:29 - error TS7006: Parameter 'effectData' implicitly has an 'any' type.

384     onWeaponEffectCreated: (effectData) => {
                                ~~~~~~~~~~

client/src/pages/Game.tsx:389:29 - error TS7006: Parameter 'effectId' implicitly has an 'any' type.

389     onWeaponEffectRemoved: (effectId) => {
                                ~~~~~~~~

client/src/pages/Game.tsx:1284:80 - error TS7006: Parameter 'open' implicitly has an 'any' type.

1284       <Dialog open={isPaused && !showGameOver && !isDialogOpen} onOpenChange={(open) => {
                                                                                    ~~~~

client/src/pages/Game.tsx:1306:50 - error TS7006: Parameter 'open' implicitly has an 'any' type.

1306       <Dialog open={showGameOver} onOpenChange={(open) => {
                                                      ~~~~

client/src/pages/Home.tsx:35:39 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

35 import { GameMode, MessageType } from '@shared/schema';
                                         ~~~~~~~~~~~~~~~~

client/src/services/aiGenerationService.ts:147:13 - error TS2322: Type 'unknown' is not assignable to type 'string | undefined'.

147             requestId: analysisResult.requestId,
                ~~~~~~~~~

  client/src/services/aiGenerationService.ts:124:5
    124     requestId?: string;
            ~~~~~~~~~
    The expected type comes from property 'requestId' which is declared here on type '{ imageUrl: string; analysis?: { recommendedType: string; colorPalette: string[]; description: string; style: string; } | undefined; requestId?: string | undefined; queuePosition?: number | undefined; }'

client/src/services/aiGenerationService.ts:148:13 - error TS2322: Type 'unknown' is not assignable to type 'number | undefined'.

148             queuePosition: analysisResult.queuePosition
                ~~~~~~~~~~~~~

  client/src/services/aiGenerationService.ts:125:5
    125     queuePosition?: number;
            ~~~~~~~~~~~~~
    The expected type comes from property 'queuePosition' which is declared here on type '{ imageUrl: string; analysis?: { recommendedType: string; colorPalette: string[]; description: string; style: string; } | undefined; requestId?: string | undefined; queuePosition?: number | undefined; }'

client/src/services/petGeneratorService.ts:2:29 - error TS2307: Cannot find module 'ethers/utils' or its corresponding type declarations.

2 import { toUtf8Bytes } from 'ethers/utils';
                              ~~~~~~~~~~~~~~

client/src/services/petGeneratorService.ts:3:27 - error TS2307: Cannot find module 'ethers/crypto' or its corresponding type declarations.

3 import { keccak256 } from 'ethers/crypto';
                            ~~~~~~~~~~~~~~~

client/src/utils/disableHmr.ts:6:33 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './unifiedWebSocket.js'?

6 import { NativeWebSocket } from './unifiedWebSocket';
                                  ~~~~~~~~~~~~~~~~~~~~

client/src/utils/webSocketUtils.ts:6:46 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './unifiedWebSocket.js'?

6 import { createWebSocket, sendMessage } from './unifiedWebSocket';
                                               ~~~~~~~~~~~~~~~~~~~~

server/memPetStorage.ts:8:8 - error TS2307: Cannot find module '@shared/petSchema' or its corresponding type declarations.

8 } from "@shared/petSchema";
         ~~~~~~~~~~~~~~~~~~~

server/postgresStorage.ts:57:58 - error TS2322: Type '"single"' is not assignable to type 'GameMode'.

57   async saveHighScore(playerName: string, score: number, gameMode: GameMode = 'single', teamName?: string): Promise<HighScore> {
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

shared/petSchema.ts:4:23 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './schema.js'?

4 import { users } from "./schema";
                        ~~~~~~~~~~


Found 176 errors in 80 files.

Errors  Files
     6  client/src/__tests__/game/controls/MobileControls.test.tsx:46
    10  client/src/components/AIPetGenerationDialog.tsx:9
     1  client/src/components/AuthProtection.tsx:3
     4  client/src/components/DualLoginButton.tsx:2
     6  client/src/components/NFTBasedPetDialog.tsx:9
     9  client/src/components/NFTBrowser.tsx:2
     8  client/src/components/NFTMintDialog.tsx:9
     1  client/src/components/NFTMintDialogProvider.tsx:3
     4  client/src/components/TournamentBattleDialog.tsx:7
     4  client/src/components/TournamentDialog.tsx:9
     5  client/src/components/TrainingFeeDialog.tsx:9
     3  client/src/components/WalletConnectButton.tsx:2
     1  client/src/components/ui/accordion.tsx:5
     2  client/src/components/ui/alert-dialog.tsx:4
     1  client/src/components/ui/alert.tsx:4
     1  client/src/components/ui/avatar.tsx:4
     1  client/src/components/ui/badge.tsx:4
     1  client/src/components/ui/breadcrumb.tsx:5
     1  client/src/components/ui/button.tsx:5
     2  client/src/components/ui/calendar.tsx:5
     1  client/src/components/ui/card.tsx:3
     2  client/src/components/ui/carousel.tsx:7
     1  client/src/components/ui/chart.tsx:4
     1  client/src/components/ui/checkbox.tsx:5
     2  client/src/components/ui/command.tsx:6
     1  client/src/components/ui/context-menu.tsx:5
     1  client/src/components/ui/dialog.tsx:5
     1  client/src/components/ui/drawer.tsx:4
     1  client/src/components/ui/dropdown-menu.tsx:5
     2  client/src/components/ui/form.tsx:13
     1  client/src/components/ui/hover-card.tsx:4
     1  client/src/components/ui/input-otp.tsx:5
     1  client/src/components/ui/input.tsx:3
     1  client/src/components/ui/label.tsx:5
     1  client/src/components/ui/menubar.tsx:5
     1  client/src/components/ui/navigation-menu.tsx:6
     2  client/src/components/ui/pagination.tsx:4
     1  client/src/components/ui/popover.tsx:4
     1  client/src/components/ui/progress.tsx:4
     1  client/src/components/ui/radio-group.tsx:5
     1  client/src/components/ui/resizable.tsx:4
     1  client/src/components/ui/scroll-area.tsx:4
     1  client/src/components/ui/select.tsx:5
     1  client/src/components/ui/separator.tsx:4
     1  client/src/components/ui/sheet.tsx:6
     8  client/src/components/ui/sidebar.tsx:6
     1  client/src/components/ui/skeleton.tsx:1
     1  client/src/components/ui/slider.tsx:4
     1  client/src/components/ui/switch.tsx:4
     1  client/src/components/ui/table.tsx:3
     1  client/src/components/ui/tabs.tsx:4
     1  client/src/components/ui/textarea.tsx:3
     1  client/src/components/ui/toast.tsx:6
     2  client/src/components/ui/toaster.tsx:1
     2  client/src/components/ui/toggle-group.tsx:5
     1  client/src/components/ui/toggle.tsx:5
     1  client/src/components/ui/tooltip.tsx:4
     1  client/src/contexts/AuthContext.tsx:2
     1  client/src/contexts/PvpAccessContext.tsx:2
     4  client/src/contexts/Web3Context.tsx:4
     1  client/src/game/battle/TestTournamentBattle.ts:1
     2  client/src/game/battle/TournamentBattleRenderer.ts:3
     1  client/src/game/engine/InputHandler.ts:3
     3  client/src/game/entities/DungeonBoss.ts:3
     1  client/src/game/pvp/PVPArenaManager.ts:5
     3  client/src/game/ui/DungeonRewardsDialog.tsx:2
     3  client/src/game/ui/PetSpecterUI.tsx:7
     2  client/src/hooks/use-toast.ts:6
     1  client/src/hooks/useMultiplayer.ts:2
     5  client/src/hooks/useTournamentBattleConnection.ts:2
     1  client/src/pages/AuthCallback.tsx:3
    16  client/src/pages/Game.tsx:2
     1  client/src/pages/Home.tsx:35
     2  client/src/services/aiGenerationService.ts:147
     2  client/src/services/petGeneratorService.ts:2
     1  client/src/utils/disableHmr.ts:6
     1  client/src/utils/webSocketUtils.ts:6
     1  server/memPetStorage.ts:8
     1  server/postgresStorage.ts:57
     1  shared/petSchema.ts